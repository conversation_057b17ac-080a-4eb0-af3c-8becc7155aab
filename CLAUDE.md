# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

Note: This repo currently contains architecture and PRD documentation only. The commands below reflect the intended tech stack and should be adapted to the actual app paths once scaffolding is added (see §Architecture Overview: Project layout suggestions).

Backend (FastAPI, Python 3.11+)
- Create venv: python -m venv .venv && source .venv/bin/activate
- Install deps (example): pip install -r apps/backend/requirements.txt
- Run dev server (example): uvicorn app.main:app --reload --port 8000
- Run tests (all): pytest -q
- Run single test file: pytest tests/test_x.py -q
- Run a single test: pytest tests/test_x.py::TestClass::test_case -q
- Lint/format (suggested): ruff check . && ruff format .

Frontend (Vite + React + TypeScript)
- Install deps (example): cd apps/frontend && npm ci
- Dev: npm run dev
- Build: npm run build
- Preview: npm run preview
- Unit tests (Vitest): npm run test
- Run single test: npx vitest run path/to/file.test.ts --filter "test name"
- Lint (ESLint): npm run lint

Monorepo notes (if using root package.json)
- Use workspaces for apps/* and packages/*
- Common scripts pattern at root: "dev:fe", "dev:be", "lint", "test"

Observability/dev ergonomics
- Always include x-trace-id on requests from frontend; backend should echo it in responses and logs.
- Prefer JSON structured logs; ensure local dev prints minimal, parseable logs.

## Architecture Overview

Authoritative specs live under docs/shards and docs/architecture.md.

System shape
- Frontend: Vite + React SPA. Right pane defaults to dynamic structured summary; original text as secondary tab. API client injects x-trace-id.
- Backend: FastAPI monolith acting as API gateway/orchestrator. Core services: ContextService (context assembly), SummaryService (rolling/incremental, versioned, fallback/ok state), LLMService adapter (model routing, retry/timeout, normalized ErrorKind), CostGuard (budget/limits), TraceMiddleware.
- Data/Infra: Supabase (Postgres/Auth/Storage) for MVP. Later: Redis + Celery for heavy tasks; light tasks via FastAPI background/edge functions.

Key contracts and flows
- SummaryPayload enforces structured nodes via Pydantic; on validation failure/timeouts/budget pressure, API returns state=fallback with empty nodes and fixed UX copy key; recovery via explicit endpoint or next successful generation.
- Unified error model with codes like STRUCTURE_PARSE_FAILED, SCHEMA_MISMATCH, PROVIDER_TIMEOUT, BUDGET_EXCEEDED; every response carries trace_id.
- Rate/budget governance: token caps per session; degrade to compressed summary before hard-failing.

Important endpoints (MVP subset)
- POST /api/sessions, GET /api/sessions, GET /api/sessions/{id}
- POST /api/sessions/{id}/messages, GET /api/sessions/{id}/messages
- GET /api/sessions/{id}/summary?version=vid
- POST /api/sessions/{id}/summary/rebuild
- POST /api/sessions/{id}/state
- POST /api/sessions/{id}/summary/fallback/recover

Project layout suggestions (from docs)
- apps/backend: FastAPI app/{api,core,services,adapters,crud,models,main.py}; tests/; requirements.txt
- apps/frontend: src/{api,components,hooks,pages,store,styles,App.tsx}; package.json; vite.config.ts
- packages/shared-types for cross-boundary contracts

Testing strategy
- Backend: pytest; mock or record LLM calls. Frontend: Vitest + RTL. Target ≥80% coverage. Include integration tests for REST flows and fallback/recover behavior.

Security and operations highlights
- Auth: MVP may allow anonymous sessions; with Supabase Auth, validate JWT and enforce RLS. HTTPS/TLS end-to-end. Secrets via platform key management.
- CI/CD: lint + unit + integration → build → deploy; ensure trace_id and fallback metrics are observable in logs/metrics.
