# 产品需求文档（PRD）— 知深学习导师 v2.0

本文档基于已对齐的项目简报与MVP验收标准，沉淀为“开发可执行”的PRD，覆盖目标与背景、范围与需求（FR/NFR）、信息架构与交互、数据与API契约、里程碑与验收、发布与度量。该PRD作为后续研发、设计、测试与运营的单一事实来源（SSOT）。

参考配套文档：
- 标准版项目简报 [`project_brief.declaration()`](docs/project-brief.md#知深学习导师—标准版项目简报single-source-of-truth)
- MVP验收标准与指标口径 [`mvp_criteria.declaration()`](docs/mvp-criteria.md#知深学习导师—mvp-验收标准与指标口径)

变更日志
- 2025-08-05 v2.0：首版开发PRD定稿，固化FR/NFR、IA/UX、数据与API契约与里程碑。作者：John (PM)
- 2025-08-05 v1.3：新增史诗列表与拆解
- 2025-08-05 v1.2：新增UI设计目标与技术假设
- 2025-08-05 v1.1：新增功能/非功能需求
- 2025-08-05 v1.0：初始草案

---

## 1. 目标与背景

目标
1) 赋能深度理解：帮助用户对复杂文本形成结构化、可迁移的深度理解。  
2) 提升学习效率：降低上下文维护与回顾负担，形成“导入—对话—回看—续学”的低摩擦闭环。  
3) 构建学习闭环：自动保存与断点续学，沉淀动态摘要为可复用知识节点。  
4) 验证核心价值：以MVP验证“动态上下文+可交互摘要”的差异化价值与成本可控性。

背景摘要
- 信息过载转向“深度消化”难题；通用AI对话缺少持续上下文与可追溯证据。  
- 知深学习导师以“双栏+动态摘要+证据优先”为核心差异化，面向知识工作者与深度学习者。

---

## 2. 范围与不在范围

In Scope（MVP）
- 纯文本粘贴导入、分段索引（offsets）
- 双栏UI：左对话、右“原文/动态摘要”切换（默认原文）
- 聚焦上下文回答（段/章级窗口）
- 滚动增量摘要；摘要节点四类（概念/证据/反例/引用）
- 摘要节点→原文单向跳转；对话气泡“引用徽章”
- 进度自动保存与断点续学
- 成本治理：Token预算器（上限/压缩/熔断）、简单双模型路由（中/重）

Out of Scope（MVP不做）
- PDF/URL/视频导入、语音模式、间隔重复、跨文档连接、社交分享、团队协作

---

## 3. 功能性需求（FR）

FR1 会话创建（粘贴导入）
- 用户可在“起始页”粘贴纯文本创建新会话。  
验收：
- 成功创建后返回 sessionId 并跳转 /session/{id}  
- 记录 Document 与 Sections（含 offsets）

FR2 内容处理与分段索引
- 后端对文本清洗、分段、建立章节/段落索引（offsetStart/offsetEnd）。  
验收：
- 分段稳定、总长度校验与异常提示  
- 存储 document 与 sections

FR3 双栏核心界面（默认“动态摘要”为主视图）
- 学习会话页采用左对话、右内容（“动态摘要/原文”Tabs），右侧默认显示“动态摘要”。  
验收：
- 默认摘要视图；Tabs可切换，保持滚动位置
- 移动端单列时，顶部 Tabs 默认“动态摘要”

FR4 对话交互
- 左栏输入框发送消息，展示往返消息流。  
验收：
- 消息状态：发送中/成功/失败重试  
- 键盘与按钮触发均可

FR5 聚焦上下文回答
- AI回答必须基于当前会话文档与聚焦章节窗口。  
验收：
- 回答附带引用徽章（至少尝试检索原文证据）  
- 未能检索到证据时需显式标注“推断”

FR6 动态摘要生成/更新（滚动增量 + 版本/回滚）
- 每次AI回答后，更新右栏“动态摘要”（四类节点）。支持摘要历史版本与一键回滚/重建。  
验收：
- 新增/更新节点可见；节点含 refs 至原文 sections offsets  
- 支持摘要重建（全量重算）与回滚到上一版本
- 点击摘要节点→原文定位并高亮 2–3 秒（UI一致行为）

FR7 视图切换与可追溯（摘要优先）
- 右栏支持“原文/动态摘要”切换；摘要节点可点击，跳转原文定位并高亮。  
验收：
- TTR（摘要点回到原文定位的平均点击）≤2  
- 误链率≤5%

FR8 进度自动保存
- 对话消息、摘要版本、阅读位置自动保存。  
验收：
- 刷新/重开浏览器后可恢复到相同状态（同会话）

FR9 断点续学（Dashboard）
- 提供历史会话列表，点击进入恢复状态。  
验收：
- 恢复左栏消息全量、右栏当前视图与滚动位置、摘要最新版本

FR10 结构化失败回退（新增）
- 当摘要结构化校验失败时，系统回退为“纯文本摘要”，禁用摘要节点的映射交互，并给出轻提示；结构化恢复成功后自动恢复交互。  
验收：
- 回退状态有明显但不打扰的提示；恢复结构化成功后自动恢复映射点击
- 该回退不影响对话继续与证据提示逻辑（与“推断标签”解耦）
- 采集与上报：回退/恢复事件需带 trace_id，上报字段含 state(from→to)、version、失败原因摘要（非隐私）
---

## 4. 非功能性需求（NFR）

- summarize 摘要生成平均&lt;1.5s（MVP目标，P95&lt;3s）；当预计超阈值时应切入异步路径并给予状态提示
- ingest 文本处理 P95&lt;60s（如触发异步解析/分段），超阈值显示进度说明与可重试入口
NFR1 平台兼容性
- 桌面端Chrome/Firefox/Safari/Edge最新版兼容；响应式布局。  
- 长任务（ingest/summarize 超阈值）需展示状态提示与可重试入口；失败提供回退（摘要回退或建议分块）

NFR2 性能
- AI响应P50<3秒（含后端编排与模型调用）；页面首屏加载<2秒（在缓存命中后）。  

NFR3 可靠性
- 数据持久化与幂等写入；会话异常中断率≤5%。可用性目标99.5%。
- 预算熔断阈值：单会话累计 token 超过 `SESSION_TOKEN_BUDGET` 或 1 分钟内请求累计 token 超过 `RATE_TOKEN_LIMIT` 触发；`retryAfterSec` 后自动恢复可重试。

NFR4 安全与隐私
- 传输与存储加密；可删除数据；日志匿名化。  

NFR5 成本效益
- Token预算与模型路由生效，单会话平均Token较基线下降≥30%。  

---

## 5. 信息架构与交互（IA/UX）

页面与关键元素
1) 起始页  
- 大输入框（粘贴文本）、“开始学习”按钮  
- 异常提示：超长/空文本/不可用字符  
2) 学习会话页（双栏）  
- 左栏：消息流、输入框、快捷操作（请求证据/用我之言重述/边界条件）  
- 右栏：Tabs（原文｜动态摘要）  
  - 原文视图：分段锚点，高亮定位  
  - 动态摘要：节点四类、可折叠、版本信息、重建/回滚按钮  
- 顶部状态：token负载/预算、模型路由指示（中/重）  
3) 历史会话列表（Dashboard）  
- 列表项：标题、时间、进度、最近摘要版本

交互规则
- 节点点击→右栏切至原文并高亮对应offset区间  
- 回答无证据→显式“推断”标签，并提供“一键请求证据”快捷按钮  
- 异常态：熔断提示→建议缩小焦点/压缩摘要后重试

---

## 6. 数据模型（简版）

Document
- id, title, createdAt  
- sections: [{id, text, offsetStart, offsetEnd, order}]

Session
- id, documentId, focusSectionId, tokenBudget, modelRoute, createdAt, updatedAt  
- viewState: { rightTab: "original|summary", scrollPositions }  
- versions: [versionId]

SummaryNode
- id, sessionId, versionId, type: concept|evidence|counter|reference  
- text, refs: [{sectionId, offsetStart, offsetEnd, confidence}]  
- meta: { createdAt, updatedAt }

ConversationMessage
- id, sessionId, role: user|assistant, content, citations: [{sectionId, offsetStart, offsetEnd, confidence, valid}]  
- costs: { promptTokens, completionTokens, model }  
- createdAt

MetricsAgg（派生）
- sessionId, process/outcome/experience/cost 指标聚合（详见验收标准）

---

## 7. API 契约（初稿）

鉴权
- MVP阶段允许匿名会话（无登录），使用 sessionToken 绑定本地存储。后续可接入 Supabase Auth。

REST Endpoints
1) POST /api/sessions  
请求：{ text: string, title?: string }  
响应：{ sessionId: string, documentId: string }

2) GET /api/sessions/{id}  
响应：{ session, document, sections, lastSummaryVersion, viewState }

3) POST /api/sessions/{id}/messages  
请求：{ content: string }  
响应：{ message: ConversationMessage, updatedSummaryVersionId?: string }

4) GET /api/sessions/{id}/messages  
响应：{ messages: ConversationMessage[] }

5) GET /api/sessions/{id}/summary?version={vid}  
响应：{ versionId, nodes: SummaryNode[] }

6) POST /api/sessions/{id}/summary/rebuild  
响应：{ versionId }

7) POST /api/sessions/{id}/state  
请求：{ viewState }  
响应：{ ok: true }

8) GET /api/sessions (Dashboard)
响应：{ sessions: Array<{id,title,updatedAt,progress}> }

错误码约定
- 400 输入非法；413 文本过长；429 预算熔断；500 内部错误
- 错误响应均返回 traceId
- Trace/Observability：统一使用 `X-Trace-Id` 作为请求/响应头；若请求无该头，服务端生成并透传；涉及回退/恢复/摘要版本操作的响应体附带 `trace_id`
- 预算熔断（429）错误体：`{ code: "BUDGET_EXCEEDED", trace_id, retryAfterSec }`

---

## 8. 业务规则与边界判定

引用有效性（valid_citation）
- 必须含 sectionId 与 offsets；与回答关键论断语义相关；未被用户标记“证据不足”。  

推断声明
- 无可引用片段时，回答需标记“推断”，并提供“请求证据”快捷入口。  

误链判定
- 偏差>200字符或跨段落视为误链，计入误链率。  

预算熔断流程
- 优先摘要压缩；仍超限则建议用户缩小焦点（切换 focusSectionId），保留会话内容不丢失。  

---

## 9. 里程碑与验收（对应 MVP）

里程碑1：可控上下文 + 滚动摘要  
- 通过：引用率≥70%、上下文丢失感≤3/5、异常中断≤5%

里程碑2：结构化摘要 + 回看效率  
- 通过：TTR≤2.0、摘要有用性≥4/5、误链≤5%、加权覆盖≥80%

里程碑3：成本与稳健性  
- 通过：均成本↓≥30%、有效引用≥80%、幻觉标记≤5%

里程碑4：学习成效与报告  
- 通过：目标达成≥4/5、重述准确≥70%、丢失感≤2/5、单位洞见成本双周↓≥15%

详细口径参见：[`mvp_criteria.declaration()`](docs/mvp-criteria.md#知深学习导师—mvp-验收标准与指标口径)

---

## 10. 测试与验收清单（抽样）

功能测试
- 粘贴创建→跳转会话→对话→更新摘要→节点跳原文→保存与续学
- 异常：超长文本/预算熔断/网络失败重试
- 回退路径：触发结构化失败→呈现固定文案→节点禁用→恢复结构化后自动启用→校验埋点回传（含 trace_id）

可用性测试
- 三场景（论文/法律/RFC）可用性走查与任务完成率  
- TTR、上下文丢失感、摘要有用性问卷

可靠性与性能
- API P50/P95响应时间  
- 会话中断率、误链率监控

安全与隐私
- HTTPS、静态数据加密、可删除数据流程

---

## 11. 发布计划与度量

发布节奏
- Week1-2：基础能力与滚动摘要  
- Week3：可追溯与版本回滚  
- Week4：预算器与路由  
- Week5：学习成效面板与导出  
- Week6：试点与稳定性

核心度量
- 有效引用率、TTR、重述准确率、目标达成度
- 单位洞见成本、会话成本下降率
- 愿意继续使用比例（≥70%为门槛）

指标定义（口径）
- TTR：摘要点至原文定位平均点击步数；目标 ≤ 2；采集自 `summary_node_clicked`→`original_scrolled_into_view`
- 误链率：误映射跳转次数 / 摘要节点跳转总次数；当 refs offsets 与实际高亮区间重叠率 < 70% 计为误链；目标 ≤ 5%
- 有效引用率：有 citations 的回答条数 / 回答总条数，按 confidence≥0.5 加权
- 单位洞见成本：总 Token / 导出次数或关键洞见数（辅助口径）

采样与窗口
- 滚动 7 天；Dashboard 展示 P50/P95 与趋势；去噪：剔除 <10s 会话、合并同一 trace 重复事件

客观指标（辅助口径）
- 导出次数（export_count）、会话时长（minutes_active）、有效引用率加权（effective_citation = 引用覆盖×置信度）
- 采集流程：前端埋点注入 trace_id，后端聚合输出并在看板提供字段检索

---

## 12. 依赖与风险

依赖
- 第三方LLM API（OpenAI/Claude/Gemini）  
- 部署与数据库服务（Vercel/Render + Supabase/自管Postgres）

风险与对策
- 动态摘要质量不稳 → 强制证据策略、回滚/重建、A/B路径  
- 成本失控 → 路由+预算器+缓存复用  
- 用户负担重 → 快捷操作与“用我之言重述”降低心智负荷

---

## 13. 附录：UI文案与状态

主要按钮/提示
- 开始学习、发送、请求证据、用我之言重述、边界条件  
- “当前回答为推断，点击请求证据以回溯原文”  
- “达到会话预算上限，已压缩摘要，建议聚焦更小范围继续”

加载与错误
- 发送中/生成中骨架屏  
- 重试与反馈通道（附 traceId）

---

本PRD与配套文档保持一致性。遇到冲突时，以本PRD为实现约束，以 [`mvp_criteria.declaration()`](docs/mvp-criteria.md#知深学习导师—mvp-验收标准与指标口径) 为验收口径参考。
<!-- PRD 对齐修订：插入 FR10 与补充 NFR2/NFR3 SLO 与异步体验说明（本注释仅为定位辅助，可在最终稿中移除） -->
