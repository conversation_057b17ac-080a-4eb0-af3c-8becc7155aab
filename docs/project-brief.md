# 知深学习导师 — 标准版项目简报（Single Source of Truth）

本文档作为统一的对齐依据，覆盖执行摘要、问题与方案、MVP范围、里程碑、指标、风险与路线图，供产品/设计/研发/运营使用。相关延伸文档：
- MVP验收标准与指标口径 [`mvp-criteria.declaration()`](docs/mvp-criteria.md#知深学习导师—mvp-验收标准与指标口径)

---

## 1. 执行摘要（Executive Summary）
- 产品概念：个人化的 AI 学习伴侣，以“对话式深度理解”为核心，帮助用户将碎片化信息转化为可应用的真知。
- 关键差异化：动态上下文管理 + 可交互摘要（摘要节点与原文/对话可追溯），避免传统AI对话“无记忆/浅层次”的问题。
- 目标用户：知识工作者与深度学习者（论文/法律/技术RFC等场景），注重可信、可追溯、能迁移的学习结果。
- MVP主路径：纯文本粘贴→分段索引→深度对话→动态摘要→摘要节点跳转原文→会话保存与续学。
- 成功标准（摘要）：可控成本（token治理）、高引用率与回看效率、用户学习目标达成与复用意愿。

---

## 2. 问题陈述（Problem Statement）
- 现状痛点：
  1) 学习浅层化：信息知道很多，难构建系统与关联。
  2) 上下文管理负担重：AI对话易中断/遗忘，用户需手动维护上下文。
  3) 可信度不足：回答缺少明确出处，难以验证。
- 现有方案不足：
  - 通用聊天：对特定长文脉络理解不足，难持续深入。
  - 稍后读：解决收集，不解决消化与理解。
  - 笔记工具：依赖用户强自律与结构化能力，成本高、效果不稳定。

---

## 3. 解决方案概述（Proposed Solution）
- 双栏式交互：左侧深度对话；右侧“原文/动态摘要”标签页切换。
- 动态上下文管理：
  - 聚焦于当前文档/章节的可控上下文窗口（段/章级），避免对话漂移。
  - 增量式“滚动摘要”维护用户理解状态与对话核心。
- 可交互摘要：
  - 节点类型：概念/证据/反例/引用。
  - 节点可点击回溯原文定位；对话消息展示引用徽章。
- 成本与稳健性：
  - Token预算器与分层模型路由（中模型默认，重问题触发大模型）。
  - 缺引用时强制追溯提示，显式“推断”标识，支持重建/回滚摘要。

---

## 4. 目标用户（Target Users）
- 知识工作者：行业报告、论文、法律文件、技术RFC等严肃文本的阅读理解。
- 终身学习者/学生：希望系统性掌握概念、进行批判性理解与迁移应用。
- 共性：追求可信、可追溯、可迁移的深度理解体验，愿投入时间与精力。

---

## 5. MVP范围（Scope）
In Scope：
1) 纯文本导入：粘贴输入，长度限制与自动分段索引。
2) 深度对话：聚焦上下文 + 动态摘要的核心学习对话。
3) 可追溯：摘要节点→原文单向跳转；对话展示引用徽章。
4) 进度追踪：会话自动保存/恢复。
5) 成本治理（基础）：Token预算上限、压缩/熔断策略。
6) FR10 结构化失败回退（新增，纳入MVP）：当摘要结构化校验失败时，回退为“纯文本摘要”，禁用摘要节点映射交互并给予轻提示；结构化恢复后自动恢复交互能力。（目的：保障对话连续性与可用性；口径对齐 PRD/架构）

Out of Scope（MVP不实现）：
- PDF/URL/视频导入、语音模式、间隔重复、跨文档知识连接、社交/分享等。

---

## 6. 关键里程碑（Milestones）
里程碑1：可控上下文 + 滚动摘要  
- 交付：文本分段/索引、滚动摘要、节点→原文跳转  
- 通过：引用率≥70%、上下文丢失感≤3/5、异常中断≤5%

里程碑2：摘要结构化与回看效率  
- 交付：四类节点、摘要历史/回滚、引用徽章  
- 通过：TTR≤2.0、摘要有用性≥4/5、误链≤5%、加权覆盖率≥80%

里程碑3：成本与稳健性治理  
- 交付：预算器、简单双模型路由、证据缺失强制追溯  
- 通过：均成本↓≥30%、有效引用≥80%、幻觉标记≤5%

里程碑4：学习成效与报告  
- 交付：自测问答/概念重述、四维指标面板、Markdown导出  
- 通过：目标达成≥4/5、重述准确率≥70%、丢失感≤2/5、单位洞见成本双周↓≥15%

更详细验收口径见：[`验收标准.declaration()`](./mvp-criteria.md:1)

---

## 7. 关键指标（KPI）
- 过程：有效追问率、引用密度、对话轮次。  
- 结果：概念重述准确率、迁移小任务完成度。  
- 体验：上下文丢失感、回看效率TTR、摘要有用性。  
- 成本：会话Token与成本、单位洞见成本（成本/被收藏或导出的摘要节点数）。

指标定义与计算伪代码参考：[`metrics.calculate()`](./mvp-criteria.md:190)

---

## 8. 核心信息架构与交互（IA & UX）
- 左栏：对话流（含引用徽章、捷径按钮：请求证据/用我之言重述/边界条件）。  
- 右栏：  
  A 原文视图（分段锚点与高亮、从摘要节点定位）；  
  B 动态摘要（四类节点、可折叠层级、版本选择与回滚）。  
- 头部：上下文负载与预算状态、模型路由指示（中/重）。  
- 错误弹性：重建/回滚摘要，确保对话可持续。

---

## 9. 技术考量（Technical Considerations）
- 前端：现代JS框架（React/Next/Vue），双栏布局与锚点高亮。  
- 后端：Node/Express 或 Python/FastAPI，负责对话编排、模型路由、会话状态。  
- AI能力：第三方LLM（OpenAI/Claude/Gemini），提示工程+状态机实现滚动摘要与可追溯策略。  
- 数据库：Firestore / MongoDB Atlas，存储文档分段、摘要版本、会话与指标。  
- 部署：Vercel/Netlify/Render，支持CI/CD与弹性伸缩。  
- 成本治理：小/中/大模型分层策略、Token预算器、热点节点缓存复用。

---

## 10. 风险与对策（Risks & Mitigations）
- 技术实现风险（高）：动态摘要质量不稳 → 分层验证（滚动摘要优先）、强制证据引用、重建/回滚与人工标注回路。  
- 运营成本风险（高）：LLM成本不确定 → 预算器、分层模型路由、缓存与节点复用、导流到中模型。  
- 竞争风险（中）：头部产品快速追随 → 强化“可追溯+可信UI+学习成效报告”的组合壁垒。  
- 用户接受度风险（中）：深度对话感知负担 → 提供捷径操作、摘要回看路径与低摩擦复盘。

---

## 11. 开放问题与验证计划（Open Questions & Validation）
- 动态摘要最佳路径：滚动增量 vs 检索引导摘要 → 两周A/B 对比一致性/证据覆盖/时延/成本。  
- “高质量学习对话”的度量：过程/结果/体验/成本四维 → 试点固定脚本+统一评测。  
- 商业模式：免费层限制 + 专业订阅（长上下文/导出/版本）、团队/课堂许可证。  
- 幻觉治理：证据块强制、推断显式、用户“证据不足”标记驱动再检索。

---

## 12. 路线图（6周建议）
- 第1周：文本分段/索引、基础RAG、最小对话流（PoC）  
- 第2周：滚动摘要与节点四分类、摘要存储与合并  
- 第3周：摘要→原文跳转、引用徽章、版本历史/回滚  
- 第4周：Token预算器、模型路由、压缩/熔断与缓存  
- 第5周：学习成效测评与指标面板、Markdown导出  
- 第6周：稳定性、埋点与看板、试点招募与访谈

---

## 13. 物料与产出（Deliverables）
- 文档：本简报、验收标准 [`mvp-criteria.declaration()`](docs/mvp-criteria.md#知深学习导师—mvp-验收标准与指标口径)  
- 原型：双栏高保真（含错误态与预算提示）  
- 技术：分段定位PoC、滚动摘要合并PoC、模型路由与预算器中间件  
- 运营：试点脚本/问卷、成本看板、异常报警阈值

---

## 14. 最小对象模型（对齐）
- Document：id, title, sections[{id, text, offsets}]  
- Session：id, documentId, focusSectionId, tokenBudget, versions  
- SummaryNode：id, type(concept|evidence|counter|reference), text, refs[{sectionId, offsetStart, offsetEnd, confidence}]  
- ConversationTurn：id, role, content, linkedRefs[SummaryNode.id], costs{prompt, completion}  
- Metrics：process/outcome/experience/cost 四类指标聚合

对象-指标映射参见：[`对象-指标映射.declaration()`](./mvp-criteria.md:140)

---

## 15. 成功标准（汇总）
- 引用率≥80%、TTR≤2、重述准确率≥70%、目标达成≥4/5、单位洞见成本双周下降≥15%，且≥70%试点用户愿意继续使用。

（完）