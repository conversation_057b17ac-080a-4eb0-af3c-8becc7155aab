# 全栈架构文档 — 知深学习导师 v2.0

本文件为导航与综述入口；单一事实源（SSOT）为 docs/shards 下的架构分片文档。本文汇总高层脉络并指向各分片。与以下文档保持一致性：
- 产品需求文档（PRD） [`prd.declaration()`](docs/prd.md:1)
- UI/UX 规格说明书 [`ux-spec.declaration()`](docs/ux-spec.md:1)
- MVP 验收标准与指标口径 [`mvp-criteria.declaration()`](docs/mvp-criteria.md:1)

---

## 1. 引言

### 1.1 文档目的
提供端到端的架构蓝图与实施约束，指导研发以最小风险交付“动态上下文+动态摘要”的核心学习体验，并确保后续演进具备良好的可迁移性与可维护性。

### 1.2 架构驱动因素
- 核心体验：支持长程对话、动态摘要（滚动增量）、可追溯（摘要节点→原文）。
- UI/UX：实现“双栏心智模型（左对话｜右内容）”，最小干扰的沉浸式学习。
- 技术选型：FastAPI 后端、Vite+React 前端、Supabase（Postgres/Auth/Storage）作为初期 BaaS。
- 演进空间：通过适配层与网关策略，支持未来迁移至自建或国内云厂商。

### 1.3 启动模板与现状
本项目为绿地项目（Greenfield）。初期按本文件新建仓库与骨架。

### 1.4 变更日志
- 2025-08-05 v2.0：定稿；补充测试、安全、可观测性策略。作者：Winston (Architect)
- 2025-08-05 v1.5：新增部署与运维策略
- 2025-08-05 v1.4：新增项目结构定义
- 2025-08-05 v1.3：新增 API 与错误模型
- 2025-08-05 v1.2：新增数据模型
- 2025-08-05 v1.1：新增高层架构与技术栈
- 2025-08-05 v1.0：引言与驱动因素

---

## 2. 高层架构（High-Level Architecture）

### 2.1 技术摘要
- 前端：Vite + React SPA，负责双栏UI、对话与摘要呈现、锚点跳转、埋点上报。
- 后端：FastAPI 单体，充当 API 网关与编排核心，提供上下文装配、模型路由、成本治理、版本化摘要。
- 基础设施（MVP）：Supabase 提供 Postgres、Auth、Storage 以加速落地。
- 第三方：LLM Provider（OpenAI/Claude/Gemini）通过适配层统一调用。

[相对V2草稿的新增/变更]
- 新增：任务编排分层策略（V2 轻任务、V2.1+ 引入 Celery/Redis 承载重任务）
- 变更：前端默认右侧为“动态摘要”主视图（原文为次级 Tab）
- 新增：CN 地域迁移路径与回滚 SOP（避免供应商锁定）
- 补充：SLO 阈值与组件依赖清单

### 2.2 架构风格
- 前端：CSR 单页应用（后续可扩展为SSR/混合渲染）。
- 后端：API Gateway + Monolith（后续提取服务的演进路径清晰）。
- 基础设施：BaaS 优先，避免前端直连 DB/Storage。

### 2.3 架构图
graph TD
    A[用户浏览器 - Vite+React SPA\n右侧默认“动态摘要”] -->|HTTPS/JSON| B[FastAPI 网关/编排]
    A -.->|认证流程| C[Supabase Auth]
    subgraph Supabase
      D[(PostgreSQL)]
      E[(Object Storage)]
      R[(Redis - V2.1+)]
    end
    subgraph 任务域
      CE[Celery - V2.1+]
      EF[Edge Functions/后台任务 - 轻任务(V2)]
    end
    subgraph 外部服务
      F[LLM Provider(s)]
    end
    B -->|连接池/SQL| D
    B -->|SDK/API| E
    B -->|LLM SDK| F
    B -->|enqueue| R
    R --> CE
    EF --> B

调用约束：
- 前端仅与 FastAPI 交互（除认证 SDK 外），严禁直连 DB/Storage。
- FastAPI 统一出入口：鉴权、路由、编排、治理、观测。

### 2.4 核心架构模式
- API 网关：统一鉴权、限流、追踪（trace_id）与错误模型。
- 适配器模式：DB/Storage/Auth/LLM 均通过接口隔离，便于供应商切换。
- 策略模式（模型路由）：按问题难度/预算策略动态选择中/重模型。
- 任务编排分层：
  - V2：轻任务（ingest/summarize 小文本）采用 FastAPI 后台任务或 Edge Functions，快速返回，失败回退。
  - V2.1+：重任务（大文件/抓取/OCR/TTS）通过 Redis 队列 + Celery Worker，支持幂等键与补偿。
- 事件/回调：摘要重建、异步进度查询（后续演进）。

## 3. 技术栈（Tech Stack）

- 前端：React 18.x、Vite 5.x、TypeScript、Zustand/Redux（状态管理）、React Router、CSS Modules/Tailwind（二选一）
- 后端：Python 3.11+、FastAPI 0.110+、Pydantic v2、SQLAlchemy + asyncpg、httpx、tenacity（重试）
- 基础设施：Supabase（Postgres 15、Auth、Storage）
- 部署：前端 Vercel/Netlify；后端 Render/Fly.io（容器化）
- 观测：结构化日志（JSON）、平台内置监控、可选 Sentry

---

## 4. 数据模型（Data Models）

### 4.1 ERD（MVP）
erDiagram
    USERS ||--o{ SESSIONS : has
    SESSIONS ||--|{ MESSAGES : contains
    SESSIONS ||--|{ SUMMARIES : has
    SESSIONS ||--|{ SECTIONS : divides
    SESSIONS ||--o{ METRICS : aggregates

    USERS {
      UUID id
      string email
      datetime created_at
    }
    SESSIONS {
      UUID id
      UUID user_id
      string title
      UUID document_id
      int focus_section_index
      json view_state
      datetime created_at
      datetime updated_at
    }
    SECTIONS {
      UUID id
      UUID document_id
      int order
      int offset_start
      int offset_end
      text text
    }
    MESSAGES {
      UUID id
      UUID session_id
      string role  // user|assistant
      text content
      json citations // [{sectionId, offsetStart, offsetEnd, confidence, valid}]
      json costs     // {promptTokens, completionTokens, model}
      datetime created_at
    }
    SUMMARIES {
      UUID id
      UUID session_id
      int version
      json nodes // [{id,type,text,refs:[{sectionId,offsetStart,offsetEnd,confidence}]}]
      datetime created_at
    }
    METRICS {
      UUID id
      UUID session_id
      json process
      json outcome
      json experience
      json cost
      datetime created_at
    }

### 4.2 关键对象契约（与 PRD 对齐）
- SummaryNode：type ∈ {concept, evidence, counter, reference}；refs 关联 SECTIONS + offsets；version 控制回滚/重建。
- ConversationMessage.citations：服务“引用徽章”与有效引用率计算。
- Session.view_state：右栏 tab（original|summary）、滚动位置、摘要版本。

---

## 5. API 设计（MVP 最小集）

鉴权：MVP 允许匿名会话（sessionToken 绑定本地）；未来接入 Supabase Auth（JWT）时，所有需要持久化的接口校验 JWT 并实施 RLS。所有请求必须携带 trace_id（header: x-trace-id 或 body.trace_id），网关统一生成与透传，详见 §9 可观测性。

### 5.1 REST Endpoints
1) POST /api/sessions
请求：{ text: string, title?: string, trace_id?: string }
响应：{ sessionId: string, documentId: string, trace_id: string }

2) GET /api/sessions
请求：headers: { x-trace-id }
响应：{ sessions: [{ id, title, updatedAt, progress? }], trace_id: string }

3) GET /api/sessions/{id}
请求：headers: { x-trace-id }
响应：{ session, document, sections, lastSummaryVersion, viewState, trace_id }

4) POST /api/sessions/{id}/messages
请求：{ content: string, trace_id?: string }
响应：{ message, updatedSummaryVersionId?, citations?, trace_id }

5) GET /api/sessions/{id}/messages
请求：headers: { x-trace-id }
响应：{ messages: ConversationMessage[], trace_id }

6) GET /api/sessions/{id}/summary?version={vid}
请求：headers/body 含 trace_id
响应：{ versionId, nodes: SummaryNode[], state: "ok" | "fallback", reason?: string, trace_id }

7) POST /api/sessions/{id}/summary/rebuild
请求：{ trace_id?: string }
响应：{ versionId, trace_id }

8) POST /api/sessions/{id}/state
请求：{ viewState, trace_id?: string }
响应：{ ok: true, trace_id }

9) POST /api/sessions/{id}/summary/fallback/recover
说明：在回退态下尝试恢复为结构化摘要（触发重建或增量校正）
请求：{ strategy?: "rebuild" | "incremental", trace_id?: string }
响应：{ versionId, state: "ok" | "fallback", trace_id }

注：state 字段用于标识当前摘要是否处于回退态（fallback）。fallback 时前端禁用节点映射等交互，详见 UX 与 §6 回退策略。

### 5.2 统一错误模型（扩展回退/预算语义）
```json
{
  "error": {
    "code": "UNIQUE_ERROR_CODE",
    "message": "用户可读错误信息",
    "details": { "field": "x", "reason": "y" },
    "trace_id": "uuid",
    "hint": "可选的引导与下一步动作"
  }
}
```
- 常用 code：VALIDATION_ERROR、BUDGET_EXCEEDED、NOT_FOUND、INTERNAL_ERROR、AUTH_REQUIRED、STRUCTURE_PARSE_FAILED、STRUCTURE_SCHEMA_MISMATCH、PROVIDER_TIMEOUT。
- 语义约定：
  - STRUCTURE_PARSE_FAILED/SCHEMA_MISMATCH：PydanticAI 校验失败 → 触发 FR10 回退（返回 state=fallback；nodes=[]；提供固定文案）
  - PROVIDER_TIMEOUT：LLM 超时 → 触发回退或重试策略，并返回 hint 引导用户
  - BUDGET_EXCEEDED：达到预算上限 → 引导缩小焦点或降低模型路由

### 5.3 速率限制与预算治理（初版）
- 每会话 token 上限（后端内存/Redis 配额可选）。
- 达上限 → 优先摘要压缩；继续超限 → 返回 BUDGET_EXCEEDED 并引导缩小焦点。
- 观测要求：返回体需包含 trace_id；服务端记录 cost_tokens、retry_count、fallback_state 以便 §9 指标聚合。

---

## 6. 后端模块设计

```
app/
 ├─ api/            # 路由层：校验、鉴权、序列化
 ├─ core/           # 配置、日志、中间件（trace_id）
 ├─ services/       # 业务编排：上下文装配、模型路由、摘要合并
 ├─ adapters/       # 适配层：db、storage、auth、llm
 ├─ crud/           # SQLAlchemy 访问（仓储）
 ├─ models/         # Pydantic Schema
 └─ main.py         # 入口
```

关键服务说明：
- ContextService：按 session.document 与 focus_section 裁剪上下文，装配 Prompt。
- SummaryService：滚动增量摘要（合并/去重/版本化），格式输出 SummaryNode；维护 state 字段：ok|fallback，并记录 reason（parse_failed/schema_mismatch/timeout/budget）。
- LLMService（Adapter）：屏蔽不同 Provider；支持中/重模型选择、重试与超时；将 provider_timeout、schema_mismatch 等失败信号标准化为 ErrorKind。
- CostGuard：预算核算、压缩策略、熔断返回；在达到上限时标记 ErrorKind=budget_exceeded，供 SummaryService 决策回退或降级。
- TraceMiddleware：生成/提取 trace_id，统一日志结构化；将 trace_id 注入到下游 services/adapters 调用上下文。

[相对V2草稿的新增/变更]
- 新增：PydanticAI 结构化输出校验引入到 summarization/answer 路径，失败回退纯文本摘要（FR10）
- 新增：LlmProvider 适配层与 PydanticAI 的边界说明；引入 ErrorKind 归一化信号
- 新增：回退态禁用交互的对外显式标记（state=fallback），恢复成功后自动复位（state=ok）

### 6.1 PydanticAI 在结构化摘要中的应用（新增）
目的：保证后端生成的摘要严格符合前端契约（节点/映射可被可靠渲染与交互）。

契约示例（Python 类型）：
```python
from pydantic import BaseModel, Field
from typing import List

class Ref(BaseModel):
    sectionId: str
    offsetStart: int
    offsetEnd: int
    confidence: float

class SummaryNode(BaseModel):
    id: str
    type: str  # concept|evidence|counter|reference
    text: str
    refs: List[Ref] = Field(default_factory=list)

class SummaryPayload(BaseModel):
    version: int
    nodes: List[SummaryNode] = Field(default_factory=list)
```

使用策略：
- 在摘要生成路径中，将 LLM 原始输出通过 PydanticAI 校验为 SummaryPayload；校验失败 → 回退为“纯文本摘要 + 空 nodes”，同时返回 state=fallback 与固定 UX 文案 key。
- 在回答路径中，若需要结构化引用（citations），也通过 Pydantic 校验字段完整性（sectionId/offsets）；失败时降级去除 citations 字段并记录 ErrorKind=citations_invalid。

与 LlmProvider 的关系：
- PydanticAI 负责“结构化契约与校验”
- LlmProvider Adapter 负责“实际模型调用与路由/重试/超时”，二者解耦；所有异常映射为 ErrorKind：{parse_failed, schema_mismatch, timeout, budget_exceeded, unknown}

### 6.2 回退（fallback）与恢复（recover）策略（新增）
- 触发条件：ErrorKind∈{parse_failed, schema_mismatch, timeout} 或预算逼近导致的降级需要
- 回退行为：
  - 返回 SummaryPayload(version 同步递增)，但 nodes=[]，state=fallback，reason=ErrorKind
  - 提供固定 UX 文案 key：summary.fallback.generic 与禁用交互提示
  - 记录指标：fallback_count+=1；fallback_reason 分布
- 恢复机制：
  - 主动恢复：POST /summary/fallback/recover 触发，策略可选 rebuild | incremental
  - 被动恢复：下一次生成成功通过 PydanticAI 校验后，state 自动切换为 ok
  - 恢复成功事件上报 metrics：recover_success+=1，latency_ms，retry_count
- 日志与追踪：
  - 在 services 层打关键 span：generate_raw → pydantic_validate → fallback_decision → persist_version
  - 所有日志均携带 trace_id，并记录 error_kind、retry、cost_tokens

### 6.3 任务编排分层（补充）
- V2 轻任务：FastAPI 后台任务/Edge Functions 处理 ingest/summarize（小文本），指数退避重试 3-5 次；最终失败 → 回退纯文本（state=fallback）
- V2.1+ 重任务：Redis 队列 + Celery Worker，使用幂等键 {entity#version#params#task}，状态机与补偿/死信队列；支持按 trace_id 聚合检索与重放


## 7. 前端应用结构

```
src/
 ├─ api/             # apiClient，统一错误与trace_id注入
 ├─ components/      # Button/Tabs/MessageBubble/SummaryNode...
 ├─ pages/           # Start/Session/Dashboard
 ├─ store/           # Zustand/Redux（session/messages/summary）
 ├─ hooks/           # useHotkeys/useScrollAnchor/useTrace
 ├─ styles/          # 全局样式/主题
 └─ App.tsx
```

实现要点：
- 双栏布局 + 可拖拽分隔；Tabs 切换保持滚动位置。
- 摘要节点点击 → 原文 Anchor 定位与高亮（平滑滚动）。
- 消息骨架屏、错误重试；顶部预算/路由状态条。
- 埋点：TTR、有效引用率、误链率、响应时间。

---

## 8. 部署与运维（DevOps）

### 8.1 前端
- 平台：Vercel/Netlify，主分支自动生产部署；PR 生成 Preview。
- 静态资源：CDN 加速、原子化发布、即时回滚。

### 8.2 后端
- 平台：Render/Fly.io；容器化部署（Dockerfile）。
- 运行时：Gunicorn/Uvicorn workers（异步），健康检查、滚动更新。

### 8.3 环境与配置
- 环境：dev / preview / prod；.env 注入（机密由平台密钥管理）。
- 数据：Supabase 项目（独立实例）；迁移由 Alembic 管理（即使使用 Supabase，依然自管迁移脚本以确保可迁移性）。

### 8.4 CI/CD（GitHub Actions）
- 作业：lint + 单测 + 集成测试 → 构建 → 部署；失败通知（Slack/邮件）。

---

## 9. 可观测性（Observability）

- 日志：JSON 结构化（level, ts, trace_id, route, latency_ms, cost_tokens, error_kind?, state?）。
- 指标：
  - 技术：请求率/错误率/延迟（P50/P95/P99）；会话 token 消耗。
  - 业务：单位洞见成本（见 PRD §11）、有效引用率、TTR、误链率。
  - 质量评分 Q（记录用、不设阻断门槛）：Q = 0.5×结构化通过率 + 0.3×引用完整率 + 0.2×用户标注正向率。
    - 结构化通过率 = 结构化摘要校验通过次数 / 摘要生成总次数
    - 引用完整率 = 有 citations 且 offsets 完整的回答次数 / 回答总次数
    - 用户标注正向率 = 用户对摘要/回答的正向反馈次数 / 总反馈次数
- 追踪：trace_id 从前端注入到后端全链路；关键步骤加 span（检索、LLM、DB、pydantic_validate、fallback_decision、recover_attempt）。
- 告警：错误率阈值、延迟阈值、预算异常（突增）告警；fallback 比例显著上升告警（同比/环比）。
- 数据出入口：
  - 前端：在 apiClient 注入 x-trace-id；埋点回传 TTR、反馈正负向、引用点击有效性
  - 后端：聚合并写入 METRICS.process/outcome/experience/cost；支持按 trace_id 和 session 维度检索

---

## 10. 测试策略（Testing）

- 单元测试：后端 Pytest；前端 Vitest + RTL；覆盖率目标 ≥ 80%。
- 集成测试：API 端到端（LLM 以 mock/record 模式）。
- 可用性测试：按 UX 验收清单（TTR、丢失感、自评问卷）。
- 负载与稳健：接口 P50<3s（含 LLM）；熔断路径与错误恢复。

---

## 11. 安全策略（Security）

- 认证：MVP 可匿名；接入 Supabase Auth 后端校验 JWT。
- 授权：行级安全（RLS）策略，确保用户数据隔离。
- 传输：全链路 HTTPS/TLS。
- 存储：最小必要数据；敏感脱敏；Storage 对象基于策略签名访问。
- 依赖安全：pip-audit / npm audit；Dependabot 升级。

---

## 12. 迁移与演进（Roadmap-Oriented Architecture）

- 供应商迁移：DB/Storage/Auth/LLM 经 adapter 隔离；配置驱动切换。
- 服务拆分：当会话/摘要达到规模后，从 Monolith 中抽出 Summary/Message 服务为独立微服务。
- 检索增强：RAG/向量库（pgvector/Weaviate/ES）作为第二阶段优化。
- 语音模式与多格式导入：通过独立边车服务与队列接入，保持网关不变。

---

## 13. 项目结构（Monorepo 建议）

```
/phoenix-learning-app
├── apps/
│   ├── backend/              # FastAPI
│   │   ├── app/{api,core,services,adapters,crud,models,main.py}
│   │   ├── tests/
│   │   └── requirements.txt
│   └── frontend/             # Vite + React
│       ├── src/{api,components,hooks,pages,store,styles,App.tsx}
│       ├── package.json
│       └── vite.config.ts
├── packages/
│   └── shared-types/
│       └── src/index.ts
├── docs/
├── .github/workflows/
├── .env.example
└── package.json
```

---

## 14. 清单：落地优先事项（与里程碑对齐）

- 后端
  - API 基座（鉴权/错误模型/trace 中间件）
  - ContextService + SummaryService（滚动增量 + 版本）
  - LLM Adapter（中/重模型路由 + 超时/重试）
  - 成本治理（预算器 + 压缩/熔断）
- 前端
  - 双栏布局 + Tabs + Anchor 定位与高亮
  - 消息流/骨架屏/错误重试
  - 顶部预算/路由状态条
  - 埋点与指标上报（TTR/引用率/误链率）
- DevOps
  - GitHub Actions（lint/测/构/发）
  - 环境变量与密钥管理
  - 监控与日志聚合

---

## 15. 附录：提示工程与策略（要点）

- 引用优先模板：回答必须引用 sectionId+offsets；缺失则标注“推断”并提供“请求证据”捷径。
- 摘要增量策略：仅合并新轮对话 delta，维持节点稳定性；必要时触发“重建摘要”。
- 模型路由：默认中模型；遇复杂指令或论证链时升级重模型；会话预算逼近时降级与压缩。
- 失败识别与降级提示（与 FR10 对齐）：
  - 当 PydanticAI 校验失败或超时，系统提示应引导用户“当前为简化摘要，部分交互已暂时禁用”，并提供“重试/恢复结构化”入口
  - 将下一步建议（缩小范围、降低复杂度）以 hint 形式回传到错误模型或响应体，供前端渲染

---

本架构文档作为开发阶段的权威技术指南。遇到重大变更，必须先更新本文件并经评审通过后再实施。