# UI/UX 规格说明书 — 知深学习导师 v1.0

本文件是视觉设计与前端实现的权威依据，定义 UX 目标、信息架构、核心用户流程、页面线框、组件与风格规范、可访问性与响应式策略，以及可测试的验收要点。与 PRD 配套使用：
- 标准版项目简报 [`project_brief.declaration()`](docs/project-brief.md#知深学习导师—标准版项目简报single-source-of-truth)
- MVP验收标准与指标口径 [`mvp_criteria.declaration()`](docs/mvp-criteria.md#知深学习导师—mvp-验收标准与指标口径)
- 产品需求文档（PRD） [`prd.declaration()`](docs/prd.md:1)

---

## 1. 引言

### 1.1 文档目的
作为视觉与前端实现的详细规范，确保：
- 与 PRD 目标一致，强调“从熟知到真知”的深度学习体验。
- 提供统一的交互模型、组件与样式约定，减少前端分歧与返工。
- 为可用性与可访问性测试提供明确的验收口径。

### 1.2 整体 UX 目标与原则
核心 UX 目标：极简、专注、无干扰；UI 服务内容与思考，降低心智负担。

目标用户画像（源自 PRD）：
- 知识工作者：论文/法律/技术文档等深度理解。
- 终身学习者/学生：系统性、批判性学习复杂课题。

核心设计原则：
- 专注优先（Focus First）：移除非必要装饰；视觉层级服务任务。
- 清晰胜于美观（Clarity Over Aesthetics）：信息结构、对比度、排版优先。
- 引导而非命令（Guide, Don’t Command）：通过布局与微文案引导操作。
- 反馈即时（Instant Feedback）：发送、切换、加载均有即时可感反馈。

### 1.3 变更日志
- 2025-08-05 v1.1：补充“摘要优先”口径、认知负荷说明、A/B 方案与 FR10 回退态规范；明确 trace_id 注入与展示。作者：Sally (UX) / Sarah (PO)
- 2025-08-05 v1.0：初稿，定义 IA、流程、线框、组件与风格。作者：Sally (UX)

---

## 1.4 认知负荷与默认视图（摘要优先）

- 认知负荷定义：用户在维持上下文与回看证据时的工作记忆占用。目标是将回看成本外化到“动态摘要 + 一键回看原文”的交互中，降低瞬时记忆需求。
- 设计口径（与 PRD/架构对齐）：
  - 右栏默认“动态摘要”为主视图；“原文”为次级 Tab。
  - 点击摘要节点支持一键定位原文并高亮 2–3 秒，辅助快速回看，减少搜索成本。
  - 在回退态（fallback）下，摘要以纯文本呈现并禁用节点映射交互，避免引发额外心智负担；恢复结构化后自动恢复交互。

验收映射：
- TTR ≤ 2（平均点击次数，从摘要节点到原文定位）
- 丢失感 ≤ 2/5（可用性问卷）
- 有用性 ≥ 4/5（摘要）

## 2. 信息架构（IA）

### 2.1 站点地图（MVP）
- 起始页（Start）→ 创建新会话
- 学习会话页（Session）↔ 历史会话列表（Dashboard）

说明：
- 信息结构保持扁平与聚焦，最大化核心学习循环的可见性。
- Dashboard 作为管理与“断点续学”入口，非必须路径，但需易达。

### 2.2 导航与可发现性
- Header：左 Logo/名称；右“历史会话”入口；起始页隐藏返回。
- 学习会话页：页面级不显示主导航，减少干扰；保留面包屑返回 Dashboard。
- 键盘导航：Tab 顺序—消息输入框优先，其次摘要/原文 Tabs，最后快捷操作。

---

## 3. 核心用户流程（User Flows）

### 3.1 流程一：创建并开始新的学习会话
入口：起始页
成功标准：跳转到 /session/{id}，右栏默认显示“动态摘要”视图（原文为次级 Tab）。

步骤：
1) 访问起始页 → 文本域粘贴内容（为空时“开始学习”按钮禁用）
2) 点击“开始学习” → 调用后端创建会话 → 返回 sessionId
3) 跳转会话页 → 双栏渲染 → 右栏“动态摘要”为默认视图（与 PRD/架构一致）
4) 系统提示：可在左栏开始向导师提问

空/异常：
- 文本为空：禁用按钮 + 占位提示
- 超长：展示错误条（含最大字符数与建议分块）
- 网络失败：Retry 行为与错误号（traceId）

### 3.2 流程二：对话式学习与动态摘要
入口：学习会话页
成功标准：用户提问得到回复；右栏“动态摘要”自动更新；引用可追溯。

步骤：
1) 用户输入问题 → 发送（Enter/按钮）
2) 左栏显示用户消息（发送中状态）→ AI 消息骨架屏
3) 后端汇总原文 + 聚焦上下文 → 调用 LLM → 返回回答 + 更新后的摘要
4) 左栏显示 AI 回答（含引用徽章）→ 右栏更新“动态摘要”
5) 用户可切换“原文/动态摘要”；点击摘要节点跳转原文并高亮

异常：
- AI 未返回引用 → 在回答上显示“推断”标签 + “请求证据”快捷操作
- Token 预算触发 → 顶部状态条提示 + 建议缩小焦点/压缩摘要重试

### 3.3 流程三：断点续学
入口：Dashboard
成功标准：点击任一历史会话进入会话页，恢复对话、摘要视图与滚动位置。

步骤：
1) 用户进入 Dashboard → 列表显示最近会话
2) 点击一条 → 跳转 /session/{id}
3) 恢复：左栏消息、右栏视图（原文/摘要）、滚动位置、摘要版本

---

## 4. 线框与页面布局（Wireframes & Layouts）

注：以下为布局规范与关键元素说明；视觉稿在设计工具中交付（Figma/Sketch）。

### 4.1 起始页（Start）
布局：垂直居中栈
- 顶部：Logo/产品名
- 核心标语：一句话价值（副标题）
- 文本粘贴域（textarea）：高度 240-320px，等宽字体可选；placeholder“在此粘贴你的学习内容…”
- 主按钮：开始学习（Primary, 大号）
- 右上角轻入口：历史会话（Link）
状态：
- 空输入禁用按钮
- 错误条：红色边框 + 辅助文本
响应式：
- ≥1280：最大宽 720px
- ≤768：按全宽排列，按钮换行

### 4.2 学习会话页（Session）
布局：左右双栏，可拖拽分隔
- 左栏（40-50%）：对话流 + 输入框
- 右栏（50-60%）：内容画布（Tabs：动态摘要｜原文）
左栏元素：
- 对话流：用户气泡右对齐、AI 气泡左对齐；时间戳灰小字；消息间 12-16px 间距
- 输入区：单行输入 + 发送按钮；支持 Shift+Enter 换行
- 快捷操作（可选）：请求证据、用我之言重述、边界条件（作为建议 Chips 或快捷菜单）
右栏元素：
- Tabs：清晰强调当前态；默认“动态摘要”
- 原文视图：段落锚点与行高 1.6-1.8；点击摘要节点后自动定位并高亮 2-3 秒
- 动态摘要：节点四类（概念/证据/反例/引用），可折叠层级；节点 hover 显示“跳原文”图标
- 回退态（FR10）：当结构化失败导致回退为纯文本摘要时：
  - 在摘要画布顶部显示 Info Banner（浅色信息条，非阻断），文案见 §9 文案规范
  - 禁用节点映射交互（按钮与链接置灰、不可点击；Tab 焦点不可达）
  - 保留“重试/恢复结构化”入口（见 §8.4）与提示（见 §9）
  - 恢复结构化成功后自动恢复映射交互与 hover 提示
顶部状态条：
- Token 预算与使用进度条；模型路由状态（中/重）；错误/熔断提示占位位于右；显示 traceId（点击复制）

### 4.3 历史会话列表（Dashboard）
布局：列表
- 顶部：标题“我的学习会话”
- 右上：新建会话按钮（跳转起始页）
- 列表项：标题（可由首行或自动生成）、最近更新时间、进度（可选），整行可点
- 空状态：友好插画/文案 +“创建新会话”按钮

---

## 5. 组件库 / 设计系统（MVP 轻量版）

命名规范：PascalCase 组件名；变体与状态明确。以下为前端实现要点（示例 props）：

### 5.1 Button 按钮
- 变体：primary | secondary | link
- 尺寸：md | lg
- 状态：default | hover | active | disabled | loading
- 无障碍：aria-label，键盘焦点可见

### 5.2 Input 输入
- 文本域 TextArea（起始页用）：字符计数/长度上限提示
- MessageInput（会话用）：支持多行与发送快捷键提示
- 错误态：边框红 + 辅助文本

### 5.3 Tabs 选项卡
- 变体：underline | pill
- 状态：active/idle/disabled
- ARIA：role="tablist/tab/tabpanel"，键盘左右切换

### 5.4 MessageBubble 消息气泡
- 类型：user | ai
- 内容：支持段落/代码/列表
- 附加：引用徽章（Chip，点击可展开引用详情）
- 状态：sending（骨架屏）| error（重试）

### 5.5 SummaryNode 节点
- 类型：concept | evidence | counter | reference
- 交互：hover 显示“跳原文”；点击 → 右栏切换原文并 Anchor 高亮
- 元信息：证据计数、最近校验时间（可选）

### 5.6 SessionListItem
- 内容：标题、更新时间
- 状态：hover 高亮；空状态展示引导

---

## 6. 品牌与风格指南（Brand & Style）

### 6.1 色彩（建议）
- 文字主色：#111827（深灰蓝）
- 背景：#FFFFFF / #F7F7FB（浅灰）
- 强调色（主按钮/链接）：#2563EB（柔和蓝）
- 辅助：成功 #10B981、警告 #F59E0B、错误 #EF4444、信息 #3B82F6
- 分隔线/边框：#E5E7EB

对比度：
- 正文文字与背景对比 ≥ 7:1（AA+）
- 次级文字 ≥ 4.5:1

### 6.2 字体与排版
- 字体：无衬线（Inter/Source Sans）；可选正文字体增加学术感（Source Serif Pro）
- 基准字号：16px；H1 28-32 / H2 22-24 / H3 18-20
- 行高：正文 1.6-1.8；对话气泡 1.5
- 段间距：正文 12-16px

### 6.3 图标
- 风格：线性、简洁（Lucide/Feather）
- 用量克制：发送、返回、跳转原文、历史会话等

### 6.4 间距与栅格
- 页面容器：最大宽 1200-1280px
- 栅格：12 列；侧边留白 ≥ 24px；组件内边距 ≥ 12px

---

## 7. 可访问性（A11y）与响应式

- 键盘可达：所有交互元素可 Tab 访问；焦点可见（focus ring）
- ARIA：Tabs、列表、按钮、状态信息（live region 展示生成中状态）
- 屏幕阅读器：消息发送与 AI 回复到达时，announcer 辅助阅读
- 动效：减少动效偏好（prefers-reduced-motion）遵循
- 响应式：
  - ≥1440：双栏宽松显示
  - 1024-1440：标准布局
  - 768-1024：可收起右栏或改为上下分区
  - <768：建议仅核心功能；Tabs 置顶切换

---

## 8. 微交互与状态

### 8.1 发送与生成
- 发送中：右侧骨架屏（摘要与 AI 消息）
- 生成完成：淡入呈现；引用徽章自右上角出现
- 错误：消息卡内显示“重试”与“复制错误信息”；顶栏 traceId

### 8.2 摘要与原文跳转
- 点击摘要节点：右栏切换原文 → 自动滚动到对应 offsets → 高亮 2-3 秒（背景淡黄）
- 无效链接防抖：偏差 >200 字符提示“定位不准，可手动修正”（MVP 可延后）

### 8.3 预算与模型路由提示
- 顶部进度条：接近上限变黄，超限变红并浮出说明
- 文案建议：简短可操作，如“已压缩摘要，建议缩小章节范围后重试”

---

## 8.4 FR10 回退与恢复（UI 规范）

场景触发：
- 后端响应 state=fallback，reason ∈ {parse_failed | schema_mismatch | timeout | budget}

UI 要求：
- Banner（信息提示，非阻断，位于摘要画布顶部）
  - 样式：信息色（蓝），带“i”图标；可关闭（仅隐藏本次）
  - 文案：见 §9
- 映射交互禁用：
  - 摘要内所有“跳原文/引用详情”入口置灰禁用，提供 aria-disabled
  - Tab/键盘焦点不可达，屏幕阅读器读出“当前为简化摘要，映射交互暂不可用”
- 恢复入口：
  - 次级按钮“恢复结构化”（触发 POST /summary/fallback/recover；策略默认 incremental）
  - 重试：若 Reason=timeout，提供“重试生成”按钮（触发消息重算或摘要重建）
- 观测/trace：
  - 在 Banner 右侧显示 traceId（可复制）；点击“更多”打开问题排查说明链接

可访问性：
- live region 宣告“进入简化摘要（回退态），部分交互已禁用”，恢复后宣告“摘要结构化已恢复”

## 9. 文案规范（选摘）

- 起始页占位：在此粘贴你的学习内容…
- 主按钮：开始学习
- 输入框占位：向导师提问或输入 / 来查看快捷指令…
- 快捷指令：请求证据｜用我之言重述｜边界条件
- 推断标签：当前回答为推断，点击“请求证据”以回溯原文
- 熔断提示：达到会话预算上限，已尝试压缩摘要。建议缩小聚焦范围后继续。
- 回退态 Banner（FR10）：当前为简化摘要，部分交互已暂时禁用。你可以稍后重试或点击“恢复结构化”。
- 恢复成功 Toast：摘要结构化已恢复，可正常使用映射交互。
- Trace 显示位：问题追踪号 traceId: {xxxx}（点击复制）

---

## 10. A/B 实验（摘要优先 vs 原文优先）

目的：
- 验证“摘要优先”对回看效率与主观体验（丢失感/有用性）的提升幅度

实验设计：
- 分组：A=摘要优先（默认“动态摘要”）；B=原文优先（默认“原文”）
- 流量：50% / 50%
- 样本量：≥ 200 会话/组
- 时长：2 周
- 指标：
  - TTR（摘要到原文定位点击数，越低越好）
  - 丢失感（1-5）
  - 有用性（1-5）
  - 二次回看率（revisit 原文或摘要的比例）
  - 有效引用率（回答含有效引用的比例）
- 记录要求：
  - 前端埋点需在 apiClient 注入 x-trace-id，并在会话维度持久（见 §11 联调）
  - 将分组标签 ab_group 注入会话级埋点，后端聚合可按 ab_group/trace_id/session 检索
- 成功准则（建议）：
  - A 组较 B 组：TTR 降低 ≥ 10%，丢失感下降 ≥ 0.3 分，有用性提升 ≥ 0.3 分；其一达成且其余不劣于基线即可通过

## 11. 可测试的 UX 验收要点

与验收指标映射：
- 回看效率 TTR ≤ 2（摘要→原文定位点击数）
- 有效引用率 ≥ 80%（回答至少含 1 个有效引用）
- 上下文丢失感 ≤ 2/5（自评问卷）
- 空/错误态：可达、可理解、可恢复
- 键盘导航：全流程无障碍，焦点顺序合理

可用性走查脚本：
1) 粘贴 2-5 千字文本 → 成功创建会话  
2) 询问 2-3 个问题 → 观察 AI 引用与摘要更新  
3) 在摘要中点击 3 个节点 → 验证定位与高亮  
4) 切换 Tab 数次 → 检查滚动位置记忆  
5) 刷新页面 → 检查断点续学恢复情况

---

## 12. 交付与工单建议

- 设计交付（Figma）：起始页、会话页、Dashboard 高保真 + 交互流动效注释
- 前端工单：
  - Layout：双栏与可拖拽分隔
  - Tabs + 高亮定位（Anchor + smooth scroll）
  - Message 流骨架屏与错误重试
  - 顶部预算/路由状态条（含 traceId 显示位与复制）
  - 后端联调点：
    - 消息返回结构包含 citations 与 summary 版本
    - 摘要节点结构含 refs（sectionId + offsets）
    - 摘要/回答响应包含 state 与 trace_id；回退/恢复入口调用后端 recover 接口
  - 质检：
    - TTR、有效引用率、误链率、ab_group、回退/恢复事件、Q 评分输入项埋点
    - A11y 自动化（axe）基本检查

---

## 13. 附录：开发对齐清单

- 组件命名一致、变体明确
- 引用徽章与“推断”标签逻辑一致
- 摘要节点点击 → 原文定位与高亮一致
- 键盘导航与 ARIA 角色一致
- 文案/状态与错误码映射一致
- FR10 回退态：state=fallback → Banner/禁用映射/恢复入口；恢复后状态宣告
- trace_id：apiClient 注入与 UI 显示位一致

（完）