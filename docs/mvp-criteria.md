# 知深学习导师 — MVP 验收标准与指标口径

本文档定义 MVP 阶段的验收标准（通过/不通过门槛）、采集与校验方法、样本与统计口径，以及风险处置与复盘要求。与 PRD、UX、架构文档保持一致，并作为产品与研发、测试的共同验收依据。

## 1. 总则
- 范围：MVP（单文档、纯文本粘贴、双栏会话、动态摘要）
- 原则：以用户价值为首、以可观测数据为准、以可复现实验为凭

## 2. 指标分层（四象限）
- Process（过程）：请求/延迟/重试/预算
- Outcome（结果）：引用有效性/误链率/覆盖率
- Experience（体验）：TTR/丢失感（1-5）/有用性（1-5）
- Cost（成本）：Token 使用与单位洞见成本

## 2. 里程碑与验收标准

### 里程碑1：可控上下文 + 滚动摘要可用
交付物：
- 文本清洗/分段（含 offsets）
- 聚焦上下文窗口（章节/段落级）
- 滚动增量摘要（rolling summary）
- 摘要节点可点击跳原文定位（单向）

验收指标（通过门槛）：
- 参与者 n≥10、场景≥3（论文方法/技术RFC/法律条款对比）
- 上下文丢失感（自评 1-5）均值 ≤ 3.0
- 回答含有效引用比例 ≥ 70%（定义见 3.2）
- 会话异常中断率（技术错误导致不可继续） ≤ 5%

### 里程碑2：摘要结构化与回看效率
交付物：
- 摘要节点四分类：concept（概念）/evidence（证据）/counter（反例）/reference（引用）
- 摘要历史版本管理与一键回滚/重建
- 对话消息展示引用徽章

验收指标（通过门槛）：
- 回看效率 TTR（从摘要节点返回原文定位的平均点击次数） ≤ 2.0
- 用户“摘要有用性”评分 ≥ 4/5
- 链接误跳转率 ≤ 5%
- 加权覆盖率（概念节点附带证据/引用的比例） ≥ 80%

### 里程碑3：成本与稳健性治理
交付物：
- Token 预算器（每轮/会话上限、压缩/熔断策略）
- 简单双模型路由（中模型默认、重问题触发大模型）
- 引用缺失时的强制追溯提示（要求给出出处或标识为推断）

验收指标（通过门槛）：
- 单会话平均Token消耗较基线下降 ≥ 30%
- 回答含有效引用比例 ≥ 80%
- 被用户标记为“幻觉/不充分证据”的比例 ≤ 5%

### 里程碑4：学习成效度量与报告
交付物：
- 轻量化测评：自测问答/概念重述（自动判分或半自动）
- 结果面板：过程/结果/体验/成本 四维指标
- Markdown 导出动态摘要

验收指标（通过门槛）：
- 学习目标达成自评 ≥ 4/5
- 概念重述准确率 ≥ 70%（定义见 3.3）
- 上下文丢失感（自评） ≤ 2/5
- 单位洞见成本（定义见 3.4）持续下降趋势显著（两周相对降幅 ≥ 15%）

---

## 3. 技术指标（Tech KPIs）
- API 延迟：P50 < 3s，P95 < 5s（含 LLM）
- 可用性：会话异常中断率 ≤ 5%，可用性 99.5%
- 可靠性：错误率 ≤ 2%
- 预算治理：熔断后具备可恢复路径（缩小范围/压缩后重试）

## 4. 结果与体验指标（Outcome & Experience）
- 有效引用率 ≥ 80%
- 误链率 ≤ 5%（偏差>200字符或跨段）
- 回看效率 TTR ≤ 2（摘要节点到原文定位的平均点击）
- 丢失感 ≤ 2/5（自评问卷，Likert）
- 有用性 ≥ 4/5

### 3.2 引用有效性（valid_citation）
定义为同时满足：
1) 引用带有原文 sectionId 与 offset 范围；  
2) 引用文本与回答关键论断具有语义相关性（由启发式/小模型判定）；  
3) 用户未在该条消息上标记“证据不充分”。  
指标：回答含至少 1 个有效引用的比例 = valid_citation_responses / all_responses

### 3.3 结果类指标
- 概念重述准确率（paraphrase_accuracy）：
  说明：用户要求“用我的话重述X”后，系统生成的重述是否忠于原文与用户目标。  
  口径：由标注集/评审问卷或半自动打分（相似度+结构对齐）得到，阈值≥70%判为正确。
- 迁移小任务完成度（transfer_success）：
  说明：将所学概念应用到新案例/小题的完成与正确情况。  
  口径：任务定义统一，人工或规则判定是否完成与正确。

### 3.4 体验类指标
- 上下文丢失感（lost_context_score）：用户对“系统是否忘记上下文”的主观评分（1-5，低为好）。  
- 回看效率 TTR（trace-to-reference）：从摘要节点回到原文定位所需点击次数的平均值。

## 5. 单位洞见成本（Cost per Insight, CPI）
定义：在稳定使用下，单位“有效洞见”所需的资源（时间/Token）的综合性辅助指标，不作为阻断门槛，仅用于迭代对比。
- 采集字段
  - export_count：导出次数（或有效收藏）
  - minutes_active：会话活动时长
  - effective_citation：有效引用率的加权（覆盖×置信度）
  - trace_id：端到端检索用
- 计算口径与看板
  - 后端聚合输出 metrics.cost 与 metrics.outcome 字段；看板按 session 与 trace_id 可检索
  - 与 PRD §11 指标定义一致

---

## 6. A/B 实验（摘要优先 vs 原文优先）
- 分组：A=摘要优先；B=原文优先
- 流量：50% / 50%
- 样本量：≥ 200 会话/组；时长 2 周
- 指标对比：TTR、丢失感（1-5）、有用性（1-5）、二次回看率、有效引用率
- 成功准则（建议）：A 组较 B 组 TTR 下降 ≥10%，且丢失感下降 ≥0.3 或有用性提升 ≥0.3；其一达成且其余不劣于基线即通过
- 追踪：ab_group、trace_id 必须贯通；看板可按 ab_group 检索

---

## 4. 数据采集与埋点

事件与属性（建议最小集）：
- document.imported {docId, length, sections}  
- session.started {sessionId, docId, focusSectionId}  
- convo.turn {sessionId, turnId, role, token_in, token_out, model_route}  
- summary.updated {sessionId, versionId, nodes_added, nodes_updated}  
- node.clicked {sessionId, nodeId, type, to=sectionId, offsets}  
- message.citation {sessionId, messageId, citations:[{sectionId, offsets, valid?}]}  
- user.mark {type: favorite|suspicious|insight, nodeId/messageId}  
- session.ended {sessionId, tokens_total, cost_total}

权限与隐私：
- 匿名化 sessionId 与 docId；文本仅做片段级引用留存，遵守用户数据删除请求。

---

## 7. FR10 回退态（结构化失败）验收
- 触发条件：摘要结构化校验失败（parse_failed/schema_mismatch/timeout）
- 前端行为：展示“简化摘要”Banner（非阻断）；禁用节点映射交互；提供“恢复结构化”入口
- 恢复：成功后自动恢复交互，并 Toast 提示
- 采集与校验：
  - 回退事件：fallback_count、fallback_reason（分布）
  - 恢复事件：recover_attempts、recover_success、recover_success_rate
  - trace_id 贯通：请求与响应需可检索；抽样核对日志与前端埋点一致性

质性访谈聚焦：
- 何时感觉“被AI带着走” vs “被打断/丢失上下文”
- 哪些节点被标记为洞见，为什么可信/有帮助
- 何时需要“回滚/重建摘要”，原因与期望

---

## 6. 争议与边界情形的判定规则

- 若回答无引用但为“明确推断”，必须显式标注“推断”，并提供请求证据的捷径按钮；否则计入幻觉嫌疑。  
- 节点链接到原文偏差>200字符或跨段落，视为误链。  
- 当会话触发 Token 熔断，系统需优先压缩摘要而非删除引用；若仍超限，提示用户缩小焦点。

---

## 8. Q 质量评分（记录项，不设阻断门槛）
目的：作为质量趋势观察的综合指标，辅助而非替代主指标
- 定义：Q = 0.5×结构化通过率 + 0.3×引用完整率 + 0.2×用户标注正向率
  - 结构化通过率 = 结构化摘要校验通过次数 / 摘要生成总次数
  - 引用完整率 = 有 citations 且 offsets 完整的回答次数 / 回答总次数
  - 用户标注正向率 = 用户对摘要/回答的正向反馈次数 / 总反馈次数
- 记录与看板：
  - 后端落 metrics.outcome.q_score（或其三项明细）；前端反馈入口需绑定 trace_id
  - 看板支持按版本、模型、会话维度聚合
- 使用方式：仅做记录，不设验收门槛；若连续两个版本 Q 下降≥10%，需给出分析与修复计划

## 9. 采集与校验（Observability & QA）
- 前端：
  - apiClient 注入 x-trace-id；所有关键交互（点击/切换/回退/恢复/反馈）回传 trace_id
  - 埋点：TTR、有效引用率、误链率、二次回看率、ab_group、回退/恢复事件、用户反馈
- 后端：
  - 日志：JSON 结构（level, ts, trace_id, route, latency_ms, cost_tokens, error_kind?, state?）
  - 指标：fallback_ratio、recover_success_rate、retry_count、单位洞见成本字段
  - 聚合：METRICS.process/outcome/experience/cost；支持按 trace_id 与 session 维度检索
- QA 抽样：
  - 抽样 5% 会话进行人工核对：引用有效性、误链判定与自动统计一致
  - 对回退/恢复链路进行全链条检查（前端提示/禁用、接口响应、日志/指标）