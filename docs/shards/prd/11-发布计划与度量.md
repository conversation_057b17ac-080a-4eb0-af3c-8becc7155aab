# 11 发布计划与度量（源自 PRD §11）

来源：../prd.md §11（行272–285）

## 11.1 发布节奏
- Week1-2：基础能力与滚动摘要  
- Week3：可追溯与版本回滚  
- Week4：预算器与路由  
- Week5：学习成效面板与导出  
- Week6：试点与稳定性

## 11.2 核心度量
- 有效引用率、TTR、重述准确率、目标达成度  
- 单位洞见成本、会话成本下降率  
- 愿意继续使用比例（≥70% 为门槛）

### 11.2.1 指标定义（公式口径）
- TTR（摘要点至原文定位点击次数）：平均每次从摘要节点跳转至原文的点击步数；目标 ≤ 2
  - 采集：前端记录 `summary_node_clicked` → `original_scrolled_into_view` 的点击数差
- 误链率：误映射跳转次数 / 摘要节点跳转总次数；目标 ≤ 5%
  - 判定：当 `refs` offsets 与实际高亮区间重叠率 < 阈值（如 < 70%）即计为误链
- 有效引用率：有 citations 的回答条数 / 回答总条数，按置信度加权（confidence≥0.5 计入权重）
- 单位洞见成本：cost_per_insight = 总 Token / 导出次数或关键洞见数（辅助口径）

### 11.2.2 采样与窗口
- 窗口：滚动 7 天；Dashboard 展示 P50/P95 与趋势
- 去噪：剔除时长 < 10s 的异常会话；合并同一 trace 的重复事件

交叉引用
- 指标与埋点：[`architecture.observability.declaration()`](../../architecture.md:318)
- 验收口径细则：[`mvp_criteria.declaration()`](../../mvp-criteria.md:1)