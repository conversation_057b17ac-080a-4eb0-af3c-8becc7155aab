# 10 测试与验收清单（抽样）— 聚焦分片（源自 PRD §10）

来源：../prd.md §10（行253–271）

## 10.1 功能测试
- 粘贴创建 → 跳转会话 → 对话 → 更新摘要 → 节点跳原文 → 保存与续学
- 异常：超长文本 / 预算熔断 / 网络失败重试
- 回退路径：触发结构化失败 → 呈现固定文案 → 节点禁用 → 恢复结构化后自动启用 → 校验埋点回传（含 trace_id）

## 10.2 可用性测试
- 三场景（论文 / 法律 / RFC）可用性走查与任务完成率
- 指标：TTR、上下文丢失感、摘要有用性问卷

## 10.3 可靠性与性能
- API P50 / P95 响应时间
- 会话中断率、误链率监控

## 10.4 安全与隐私
- HTTPS、静态数据加密、可删除数据流程

## 10.5 采集与校验（客观指标）
- 采集：导出次数（export_count）、会话时长（minutes_active）、有效引用率加权（effective_citation = 引用覆盖×置信度），前端埋点注入 trace_id
- 校验：后端聚合输出可被看板检索；随机抽样对齐人工标注与自动统计的一致性（质量保证）

交叉引用
- 指标口径与阈值：[`mvp_criteria.declaration()`](../../mvp-criteria.md:1)
- 观测体系：[`architecture.observability.declaration()`](../../architecture.md:318)
- FR10 回退路径：[`prd.FR10.declaration()`](../prd.md:103)