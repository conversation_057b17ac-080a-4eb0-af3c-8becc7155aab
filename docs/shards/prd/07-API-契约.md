# 07 API 契约（MVP 初稿）— 聚焦分片（源自 PRD §7）

来源：../prd.md §7（行180–217）

## 7.1 鉴权
- MVP 阶段允许匿名会话（无登录），使用 sessionToken 绑定本地存储。后续可接入 Supabase Auth。

## 7.2 REST Endpoints
1) POST /api/sessions
请求：{ text: string, title?: string }
响应：{ sessionId: string, documentId: string }

2) GET /api/sessions/{id}
响应：{ session, document, sections, lastSummaryVersion, viewState }

3) POST /api/sessions/{id}/messages
请求：{ content: string }
响应：{ message: ConversationMessage, updatedSummaryVersionId?: string }

4) GET /api/sessions/{id}/messages
响应：{ messages: ConversationMessage[] }

5) GET /api/sessions/{id}/summary?version={vid}
响应：{ versionId, nodes: SummaryNode[] }

6) POST /api/sessions/{id}/summary/rebuild
响应：{ versionId }

7) POST /api/sessions/{id}/state
请求：{ viewState }
响应：{ ok: true }

8) GET /api/sessions (Dashboard)
响应：{ sessions: Array<{id,title,updatedAt,progress}> }

## 7.3 错误码约定
- 400 输入非法；413 文本过长；429 预算熔断；500 内部错误
- 错误响应均返回 traceId
- 埋点与追踪：所有与回退/恢复/摘要版本操作相关接口应接受/传递 trace_id（header 或 body），用于端到端追踪

## 7.4 Trace/Observability 规范
- Header 统一：请求/响应均使用 `X-Trace-Id`；若请求无该头，服务端生成并在响应透传
- 透传规则：服务端在日志与指标中记录 trace_id，并在涉及回退/恢复/摘要版本接口的响应体中附带 `trace_id`
- 客户端注入：前端埋点、错误上报、回退/恢复事件需带入相同 `X-Trace-Id`

### 7.4.1 FR10 结构化失败回退出入/恢复事件 Schema
- 事件名：`summary_structured_state_changed`
- 请求体：
  ```json
  {
    "trace_id": "<uuid>",
    "sessionId": "<id>",
    "version": "<versionId>",
    "state": {"from": "structured|plain", "to": "structured|plain"},
    "reason": "<非隐私失败原因摘要>",
    "ts": "ISO8601"
  }
  ```
- 响应体：`{ ok: true, trace_id }`

## 7.5 备注
- 引用徽章与有效引用率依赖 ConversationMessage.citations 字段完备性
- 摘要版本化需保证 SummaryNode 结构一致性，支持重建与回滚
- 回退/恢复事件需落日志与指标（trace_id 贯通），用于观测看板检索

交叉引用
- 架构 API 设计与错误模型：[`architecture.api.declaration()`](../../architecture.md:166)
- 结构化校验与回退：[`architecture.pydantic_ai.declaration()`](../../architecture.md:241)
- 数据对象契约：[`architecture.data_models.declaration()`](../../architecture.md:159)
- 可观测性追踪：[`architecture.observability.declaration()`](../../architecture.md:318)