# 03 功能性需求（FR）— 聚焦分片（源自 PRD §3）

来源：../prd.md §3（行48–108, 254–260）

## FR1 会话创建（粘贴导入）
验收
- 成功创建后返回 sessionId 并跳转 /session/{id}
- 记录 Document 与 Sections（含 offsets）

## FR2 内容处理与分段索引
验收
- 分段稳定、总长度校验与异常提示
- 存储 document 与 sections

## FR3 双栏核心界面（默认“动态摘要”为主视图）
验收
- 默认摘要视图；Tabs 可切换，保持滚动位置
- 移动端单列时，顶部 Tabs 默认“动态摘要”

## FR4 对话交互
验收
- 消息状态：发送中/成功/失败重试
- 键盘与按钮触发均可

## FR5 聚焦上下文回答
约束
- AI 回答必须基于当前会话文档与聚焦章节窗口
验收
- 回答附带引用徽章（至少尝试检索原文证据）
- 未能检索到证据时需显式标注“推断”

## FR6 动态摘要生成/更新（滚动增量 + 版本/回滚）
验收
- 新增/更新节点可见；节点含 refs 至原文 sections offsets
- 支持摘要重建（全量重算）与回滚到上一版本
- 点击摘要节点→原文定位并高亮 2–3 秒（UI一致行为）

## FR7 视图切换与可追溯（摘要优先）
验收
- TTR（摘要点回到原文定位的平均点击）≤2
- 误链率≤5%

## FR8 进度自动保存
验收
- 刷新/重开浏览器后可恢复到相同状态（同会话）

## FR9 断点续学（Dashboard）
验收
- 恢复左栏消息全量、右栏当前视图与滚动位置、摘要最新版本

## FR10 结构化失败回退（新增）
说明
- 当摘要结构化校验失败时，系统回退为“纯文本摘要”，禁用摘要节点的映射交互，并给出轻提示；结构化恢复成功后自动恢复交互
- UX 降级细节（固定文案与行为）：
  - 提示文案（逐字）：“当前为纯文本摘要（结构化校验未通过），已暂时禁用节点映射，待结构化恢复将自动启用。”
  - 行为：节点不可点击跳转原文；映射入口灰化；轻提示 6–8 秒或可关闭 Banner；恢复结构化后自动启用映射点击
- 采集与上报：回退/恢复事件需带 trace_id，上报字段含 state(from→to)、version、失败原因摘要（非隐私）
验收
- 回退状态有明显但不打扰的提示；恢复结构化成功后自动恢复映射点击
- 该回退不影响对话继续与证据提示逻辑（与“推断标签”解耦）
- 埋点完整：出现回退与恢复事件均可在观测面板检索（trace_id 贯通）

交叉引用
- 架构：PydanticAI 结构化校验与回退 [`architecture.pydantic_ai.declaration()`](../../architecture.md:241)
- API：消息与摘要接口与追踪注入 [`architecture.api.declaration()`](../../architecture.md:170)
- 验收：MVP 指标口径与客观指标采集 [`mvp_criteria.declaration()`](../../mvp-criteria.md:1)