# PRD Shards Cross-Reference Index

目的：建立 PRD 分片间与源文档/架构文档的交叉引用，支持角色定向查阅与一致性校验（章节→源行号→相关分片/架构分片）。

来源主文档：../prd.md  
关联架构：../architecture.md

## 角色导览
- 开发（后端/平台）：03-FR、06-数据模型、07-API、08-业务规则、09-里程碑
- 前端：03-FR、05-IA/UX、07-API、10-测试与验收清单
- QA：03-FR、04-NFR、09-里程碑与验收、10-测试清单、11-度量
- PO/PM：01-目标与背景、02-范围、09-里程碑、11-度量、12-依赖与风险

## 交叉引用矩阵
- 01-目标与背景.md
  - 源：prd.md:18-29
  - 关联：架构引言/驱动因素 [`architecture.declaration()`](../architecture.md:10)
- 02-范围-不在范围.md
  - 源：prd.md:32-45
  - 关联：架构 HLA 边界/任务分层 [`architecture.declaration()`](../architecture.md:35), [`architecture.declaration()`](../architecture.md:274)
- 03-功能性需求-FR.md
  - 源：prd.md:48-108
  - 关联：架构 API 设计/错误模型 [`architecture.declaration()`](../architecture.md:166)
  - 关联：PydanticAI/回退策略（FR10）[`architecture.declaration()`](../architecture.md:241)
  - 关联：前端结构与交互（FR3/FR7）[`architecture.declaration()`](../architecture.md:278)
- 04-非功能性需求-NFR.md
  - 源：prd.md:110-129
  - 关联：可观测性与 SLO 指标 [`architecture.declaration()`](../architecture.md:318)
  - 关联：任务分层与异步阈值 [`architecture.declaration()`](../architecture.md:274)
- 05-信息架构与交互-IA-UX.md
  - 源：prd.md:132-151
  - 关联：前端实现要点与组件结构 [`architecture.declaration()`](../architecture.md:278)
- 06-数据模型.md
  - 源：prd.md:154-177
  - 关联：ERD 与对象契约 [`architecture.declaration()`](../architecture.md:100)
- 07-API-契约.md
  - 源：prd.md:180-216
  - 关联：REST 设计与统一错误模型 [`architecture.declaration()`](../architecture.md:166)
- 08-业务规则与边界判定.md
  - 源：prd.md:219-233
  - 关联：预算治理/错误码/回退策略 [`architecture.declaration()`](../architecture.md:198), [`architecture.declaration()`](../architecture.md:241)
- 09-里程碑与验收.md
  - 源：prd.md:235-249
  - 关联：落地优先事项与对齐 [`architecture.declaration()`](../architecture.md:379)
  - 关联：可观测性指标口径 [`architecture.declaration()`](../architecture.md:318)
- 10-测试与验收清单.md
  - 源：prd.md:253-269
  - 关联：测试策略（单测/集成/可用性/负载）[`architecture.declaration()`](../architecture.md:327)
- 11-发布计划与度量.md
  - 源：prd.md:272-285
  - 关联：DevOps/CI-CD 与环境策略 [`architecture.declaration()`](../architecture.md:299)
- 12-依赖与风险.md
  - 源：prd.md:288-299
  - 关联：技术栈/适配层/迁移与演进 [`architecture.declaration()`](../architecture.md:90), [`architecture.declaration()`](../architecture.md:346)
- 13-附录-UI文案与状态.md
  - 源：prd.md:301-314
  - 关联：API 错误模型与 trace_id 提示文案对齐 [`architecture.declaration()`](../architecture.md:198)

## 一致性检查要点
- FR 与 API：03-FR 的接口能力与 07-API 契约需一一对应；新增 FR 必须同步更新接口
- 结构化回退：FR10 的 UI 提示/交互禁用与架构的 PydanticAI 回退策略保持一致
- Offsets 口径：SummaryNode.refs 与 ConversationMessage.citations 的 offsets 定义一致；误链判定口径参见 11-发布计划与度量（重叠率阈值）
- NFR 与 SLO：性能/可靠性/预算等阈值与架构可观测性/任务分层阈值一致
- Trace 统一：所有相关接口请求/响应统一使用 `X-Trace-Id`，响应体附带 `trace_id`；FR10 事件遵循 07-API 事件 Schema
- 里程碑验收：PRD 里程碑指标需可通过架构埋点/观测面板取数验证
- 指标公式：TTR、误链率、有效引用率、cost_per_insight 的定义与窗口与 11-发布计划与度量一致
