# 05 信息架构与交互（IA/UX）— 聚焦分片（源自 PRD §5）

来源：../prd.md §5（行132–151）, §10（行254–260）

## 5.1 页面与关键元素
1) 起始页
- 大输入框（粘贴文本）、“开始学习”按钮
- 异常提示：超长/空文本/不可用字符

2) 学习会话页（双栏）
- 左栏：消息流、输入框、快捷操作（请求证据/用我之言重述/边界条件）
- 右栏：Tabs（原文｜动态摘要）
  - 原文视图：分段锚点，高亮定位
  - 动态摘要：节点四类、可折叠、版本信息、重建/回滚按钮
- 顶部状态：token负载/预算、模型路由指示（中/重）

3) 历史会话列表（Dashboard）
- 列表项：标题、时间、进度、最近摘要版本

## 5.2 交互规则
- 节点点击 → 右栏切至原文并高亮对应 offset 区间
- 回答无证据 → 显式“推断”标签，并提供“一键请求证据”快捷按钮
- 异常态：熔断提示 → 建议缩小焦点/压缩摘要后重试

## 5.3 回退态（FR10）UX 细节
- 提示文案（逐字）：“当前为纯文本摘要（结构化校验未通过），已暂时禁用节点映射，待结构化恢复将自动启用。”
- 行为：摘要节点不可点击跳转原文；映射入口灰化；轻提示 6–8 秒或可关闭 Banner；恢复结构化后自动启用映射点击
- 埋点：回退/恢复事件携带 trace_id，上报 state(from→to)、version、原因摘要（非隐私）

交叉引用
- FR：摘要优先与可追溯 [`prd.FR7.declaration()`](../prd.md:87)
- FR：结构化失败回退与测试路径 [`prd.FR10.declaration()`](../prd.md:103)
- PRD：测试与验收清单（回退路径） [`prd.testing.declaration()`](../prd.md:254)
- 架构：前端应用结构与交互要点 [`architecture.frontend.declaration()`](../../architecture.md:278)