# 02 范围与不在范围（源自 PRD §2）

来源：../prd.md §2（行32–45）

## 2.1 In Scope（MVP）
- 纯文本粘贴导入、分段索引（offsets）
- 双栏UI：左对话、右“原文/动态摘要”切换（默认原文）
- 聚焦上下文回答（段/章级窗口）
- 滚动增量摘要；摘要节点四类（概念/证据/反例/引用）
- 摘要节点→原文单向跳转；对话气泡“引用徽章”
- 进度自动保存与断点续学
- 成本治理：Token预算器（上限/压缩/熔断）、简单双模型路由（中/重）

## 2.2 Out of Scope（MVP 不做）
- PDF/URL/视频导入、语音模式、间隔重复、跨文档连接、社交分享、团队协作

交叉引用
- 关联功能需求：[`prd.FR.declaration()`](../prd.md:48)
- 架构边界与任务分层：[`architecture.tasks.declaration()`](../../architecture.md:274)