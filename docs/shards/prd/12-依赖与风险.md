# 12 依赖与风险（源自 PRD §12）

来源：../prd.md §12（行288–299）

## 12.1 依赖
- 第三方 LLM API（OpenAI / Claude / Gemini）
- 部署与数据库服务（Vercel / Render + Supabase / 自管 Postgres）

## 12.2 风险表（含 Owner/触发条件/缓解/时窗）
- 动态摘要质量不稳｜Owner: 后端&算法｜触发：结构化通过率<85%或fallback_ratio>15%｜缓解：证据强制/降级回退/重建/提示引导｜时窗：周
- 成本失控｜Owner: 后端｜触发：会话或全局 cost_tokens 突增>30%｜缓解：降级路由/限流/缓存/提示引导｜时窗：日
- 用户负担重｜Owner: 前端&设计｜触发：TTR>P95目标或用户负反馈上升>20%｜缓解：快捷操作/渐进暴露/引导文案优化｜时窗：周

交叉引用
- 成本治理/预算熔断：[`architecture.api.declaration()`](../../architecture.md:211)
- 任务分层与回退策略：[`architecture.tasks.declaration()`](../../architecture.md:274)