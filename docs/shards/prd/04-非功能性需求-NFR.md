# 04 非功能性需求（NFR）— 聚焦分片（源自 PRD §4）

来源：../prd.md §4（行110–129）, §7（行213–217）, §11（行287–289）

## 4.1 性能与时延
- 摘要生成平均 < 1.5s（MVP 目标，P95 < 3s）；预计超阈值时切入异步路径并给予状态提示
- ingest 文本处理 P95 < 60s（如触发异步解析/分段），超阈值显示进度说明与可重试入口
- 页面首屏加载 < 2s（缓存命中后）
- API 响应 P50 < 3s（含后端编排与模型调用）

## 4.2 平台兼容性
- 桌面端 Chrome/Firefox/Safari/Edge 最新版兼容；响应式布局
- 长任务（ingest/summarize 超阈值）需展示状态提示与可重试入口；失败提供回退（摘要回退或建议分块）

## 4.3 可靠性
- 数据持久化与幂等写入；会话异常中断率 ≤ 5%
- 可用性目标 99.5%

## 4.4 安全与隐私
- 全链路传输与存储加密；用户可删除数据；日志匿名化

## 4.5 成本效益与观测
- Token 预算与模型路由生效，单会话平均 Token 较基线下降 ≥ 30%
- 预算熔断（429）阈值与恢复：
  - 触发：单会话累计 token 超过 `SESSION_TOKEN_BUDGET` 或 1 分钟内请求累计 token 超过 `RATE_TOKEN_LIMIT`
  - 响应：HTTP 429，错误体 `{ code: "BUDGET_EXCEEDED", trace_id, retryAfterSec }`
  - 恢复：冷却时间 `retryAfterSec` 后可重试；可配置基于滑动窗口自动恢复
- 追踪与埋点：统一使用 `X-Trace-Id`；与回退/恢复/摘要版本相关接口在响应体附带 `trace_id`
- 单位洞见成本客观指标（辅助口径）采集：导出次数（export_count）、会话时长（minutes_active）、有效引用率加权（effective_citation = 引用覆盖×置信度）；前端埋点注入 trace_id，后端聚合与看板检索

交叉引用
- 架构 SLO 与任务分层：[`architecture.tasks.declaration()`](../../architecture.md:274)
- 观测与指标：[`architecture.observability.declaration()`](../../architecture.md:318)
- API 追踪口径：[`architecture.api.declaration()`](../../architecture.md:166)
- 验收指标口径：[`mvp_criteria.declaration()`](../../mvp-criteria.md:1)