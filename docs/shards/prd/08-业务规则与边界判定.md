# 08 业务规则与边界判定（源自 PRD §8）

来源：../prd.md §8（行219–233）, §7（行213–217）

## 8.1 引用有效性（valid_citation）
- 必须含 sectionId 与 offsets
- 与回答关键论断语义相关
- 未被用户标记为“证据不足”

## 8.2 推断声明
- 无可引用片段时，回答需标记“推断”
- 提供“一键请求证据”快捷入口

## 8.3 误链判定
- 偏差 > 200 字符或跨段落视为误链
- 计入误链率

## 8.4 预算熔断流程
- 优先摘要压缩
- 仍超限则建议用户缩小焦点（切换 focusSectionId）
- 保留会话内容不丢失

## 8.5 回退/恢复追踪口径
- 与回退/恢复/摘要版本相关接口需接受/传递 trace_id（header 或 body），支持端到端追踪
- 回退与恢复事件需上报 state(from→to)、version、原因摘要（非隐私），便于观测看板检索

交叉引用
- FR：聚焦上下文回答/引用徽章 [`prd.FR5.declaration()`](../prd.md:74)
- FR：结构化失败回退（含追踪要求）[`prd.FR10.declaration()`](../prd.md:103)
- 架构：预算治理/错误模型与追踪口径 [`architecture.api.declaration()`](../../architecture.md:198)