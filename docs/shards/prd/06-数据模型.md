# 06 数据模型（简版）— 聚焦分片（源自 PRD §6）

来源：../prd.md §6（行154–177）

## 6.1 核心实体
Document
- id, title, createdAt
- sections: [{id, text, offsetStart, offsetEnd, order}]

Session
- id, documentId, focusSectionId, tokenBudget, modelRoute, createdAt, updatedAt
- viewState: { rightTab: "original|summary", scrollPositions }
- versions: [versionId]

SummaryNode
- id, sessionId, versionId, type: concept|evidence|counter|reference
- text, refs: [{sectionId, offsetStart, offsetEnd, confidence}]
- meta: { createdAt, updatedAt }

ConversationMessage
- id, sessionId, role: user|assistant, content
- citations: [{sectionId, offsetStart, offsetEnd, confidence, valid}]
- costs: { promptTokens, completionTokens, model }
- createdAt

MetricsAgg（派生）
- sessionId, process/outcome/experience/cost 指标聚合（详见验收标准）

## 6.2 设计要点
- sections 以 offsets 提供原文追溯锚点，服务“摘要节点→原文定位”与“引用徽章”
- SummaryNode.refs 与 citations 的 offsets 口径一致，利于一致性校验与 TTR 统计
- viewState 明确 rightTab 与滚动位置，保障“刷新/续学”体验一致

交叉引用
- 架构 ERD 与对象契约：[`architecture.data_models.declaration()`](../../architecture.md:100)
- 指标与可观测性：[`architecture.observability.declaration()`](../../architecture.md:318)
- 验收指标口径：[`mvp_criteria.declaration()`](../../mvp-criteria.md:1)