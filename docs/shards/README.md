# 分片文档总导航 — PRD 与 Architecture

本目录作为分片文档的统一入口，汇集 PRD 与 Architecture 的聚焦分片、交叉引用以及使用说明，确保跨角色协作时的一致性与可追溯性。

子目录
- PRD 分片目录：./prd/  
  - 导航与说明：./prd/README.md  
  - 交叉引用索引：./prd/index-crossref.md
- Architecture 分片目录：./architecture/  
  - 导航与说明：./architecture/README.md  
  - 交叉引用索引：./architecture/index-crossref.md

使用说明
1) 按角色起步
- 后端/平台：Architecture 02/04/05/06/09/11 + PRD 03/06/07/08/09
- 前端：Architecture 02/07/05/09 + PRD 03/05/07/10
- QA：PRD 03/04/09/10/11 + Architecture 05/09/10
- PO/PM：PRD 01/02/09/11/12 + Architecture 02/14/12/03

2) 链路一致性校验
- Offsets 口径：SummaryNode.refs 与 ConversationMessage.citations 的 offsets 定义应一致，误链阈值为 200 字符
- 回退策略：PRD FR10（结构化失败回退）与 Architecture PydanticAI 校验/回退路径应一致
- 错误模型/预算：PRD NFR 与 Architecture API 错误模型、预算治理阈值保持同步
- 指标口径：TTR/有效引用率/误链率在 PRD/MVP 与 Architecture/Observability 一致

3) 文档冲突处理
- SSOT 原则：以 PRD/Architecture 主文档为单一事实来源（SSOT）
- 若分片与主文档冲突：以主文档为准，分片需要同步修订并记录变更

快速跳转
- PRD 主文档：../prd.md
- Architecture 主文档：../architecture.md
- MVP 验收标准：../mvp-criteria.md
- UX 规格说明：../ux-spec.md
- 项目简报：../project-brief.md

维护说明
- 新增或修改 FR/NFR/API/数据模型时，需同步更新对应分片与 crossref 索引
- 大改动（影响结构化回退、预算治理、错误模型）需在 PRD 与 Architecture 双端更新并标注版本