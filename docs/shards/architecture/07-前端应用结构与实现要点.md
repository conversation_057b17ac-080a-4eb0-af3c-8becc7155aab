# 07 前端应用结构与实现要点 — 聚焦分片（源自 架构 §7）

来源：../architecture.md §7（行278–296）

## 7.1 目录结构
```
src/
 ├─ api/             # apiClient，统一错误与 trace_id 注入
 ├─ components/      # Button/Tabs/MessageBubble/SummaryNode...
 ├─ pages/           # Start/Session/Dashboard
 ├─ store/           # Zustand/Redux（session/messages/summary）
 ├─ hooks/           # useHotkeys/useScrollAnchor/useTrace
 ├─ styles/          # 全局样式/主题
 └─ App.tsx
```

## 7.2 实现要点与分层
- 双栏布局 + 可拖拽分隔；Tabs 切换保持滚动位置
- 摘要节点点击 → 原文 Anchor 定位与高亮（平滑滚动）
- 消息骨架屏、错误重试；顶部预算/路由状态条
- 埋点：TTR、有效引用率、误链率、响应时间

分层建议
- api：统一 fetch 封装，错误模型/回退态/trace_id 注入
- store：会话/消息/摘要三 slice；selector 避免无关重渲染
- UI：容器/展示分离；长列表虚拟化（摘要节点/消息流）
- 性能基线：首屏 P50<2.5s、P95<4s；交互 P95<150ms；长列表渲染 ≤16ms/frame；错误页可用性 ≤1s

一致性约束
- 右栏默认“动态摘要”视图（与 PRD FR3/FR7 对齐）
- “推断”标签与“请求证据”快捷按钮行为与 PRD FR5 对齐
- 回退态（结构化失败）禁用节点映射与轻提示（PRD FR10）

交叉引用
- PRD 交互与 IA/UX：[`prd.ia_ux.declaration()`](../prd.md)
- 架构 API 契约：[`architecture.api.declaration()`](../architecture.md:166)
- 可观测性指标上报：[`architecture.observability.declaration()`](../architecture.md:318)