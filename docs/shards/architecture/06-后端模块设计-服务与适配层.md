# 06 后端模块设计（服务与适配层）— 聚焦分片（源自 架构 §6）

来源：../architecture.md §6（行217–240, 241–277），已纳入 FR10 回退/恢复、trace_id 贯通与观测上报

## 6.1 目录结构（Monolith 内聚）
```
app/
 ├─ api/            # 路由层：校验、鉴权、序列化
 ├─ core/           # 配置、日志、中间件（trace_id）
 ├─ services/       # 业务编排：上下文装配、模型路由、摘要合并
 ├─ adapters/       # 适配层：db、storage、auth、llm
 ├─ crud/           # SQLAlchemy 访问（仓储）
 ├─ models/         # Pydantic Schema
 └─ main.py         # 入口
```

## 6.2 核心服务说明
- ContextService
  - 依据 session.document 与 focus_section 裁剪上下文，装配 Prompt
- SummaryService
  - 滚动增量摘要（合并/去重/版本化），输出契约化的 SummaryNode；维护摘要 state: "ok"|"fallback" 与 reason，见 6.2.1
- LLMService（Adapter）
  - 屏蔽 Provider 差异；支持中/重模型选择、重试与超时；将失败映射为标准 ErrorKind
- CostGuard
  - 预算核算、压缩策略、熔断返回（与 PRD 的预算治理对应）；达上限时返回 ErrorKind=budget_exceeded
- TraceMiddleware
  - 生成/提取 trace_id 并透传上下文；统一结构化日志字段

### 6.2.1 ErrorKind 与状态机
- ErrorKind 枚举：parse_failed | schema_mismatch | timeout | budget_exceeded | citations_invalid | unknown
- 摘要状态机
  - 正常 →（ErrorKind ∈ {parse_failed,schema_mismatch,timeout}）→ fallback
  - fallback →（rebuild/incremental 成功校验）→ ok
- 对外行为：当 state=fallback 时，响应体需显式返回 state=fallback 与 reason，用于前端禁用映射交互

## 6.3 PydanticAI 在结构化摘要中的应用（与回退策略）
契约示例（Python 类型，见主文档）
- SummaryPayload(version, nodes[])；SummaryNode(id, type, text, refs[])
使用策略
- 将 LLM 原始输出通过 PydanticAI 校验为 SummaryPayload
- 校验失败 → 回退为“纯文本摘要 + 空 nodes”，并返回 state=fallback 与固定 UX 文案 key
- 回答路径需要 citations 时，同步以 Pydantic 校验字段完整性；失败则降级去除 citations 并记 ErrorKind=citations_invalid

与 LlmProvider 的关系
- PydanticAI：结构化契约与校验
- LlmProvider Adapter：实际模型调用、路由/重试/超时，二者解耦；异常统一映射为 ErrorKind

## 6.4 回退（fallback）与恢复（recover）策略（新增）
- 触发条件：ErrorKind∈{parse_failed, schema_mismatch, timeout} 或预算逼近的降级需要
- 回退行为：
  - 版本仍递增；nodes=[]；state=fallback；reason=ErrorKind
  - 固定文案 key：summary.fallback.generic；禁用节点映射交互
- 恢复机制：
  - 主动恢复：POST /summary/fallback/recover，strategy∈{rebuild|incremental}
  - 被动恢复：下一次生成校验通过即自动切换为 ok
- 指标与日志：
  - 记录 fallback_count、recover_success、retry_count、latency_ms、cost_tokens
  - 日志携带 trace_id、error_kind、state；在 generate_raw/pydantic_validate/fallback_decision/recover_attempt 处打 span

## 6.5 任务编排分层
- V2 轻任务
  - ingest/summarize（小文本）由 FastAPI 后台任务或 Edge Functions 处理
  - 指数退避重试 3–5 次，最终失败 → 回退纯文本（state=fallback）
- V2.1+ 重任务
  - Redis 队列 + Celery Worker
  - 幂等键：{entity#version#params#task}
  - 状态机与补偿策略、死信队列；支持按 trace_id 聚合检索与重放

## 6.6 与前后端契约一致性要点
- SummaryNode.refs 与 ConversationMessage.citations 的 offsets 口径一致
- 回退状态对前端可见（state=fallback；禁用节点映射交互；固定文案），结构化恢复后自动恢复交互
- trace_id 自前端注入后，全链路透传至日志/告警；响应体需回传 trace_id 便于端到端排障

交叉引用
- PRD 结构化失败回退：[`prd.FR10.declaration()`](../prd.md)
- 架构 PydanticAI 校验：[`architecture.pydantic_ai.declaration()`](../architecture.md:241)
- 架构 任务分层：[`architecture.tasks.declaration()`](../architecture.md:274)
- API 与错误模型：[`architecture.api.declaration()`](../architecture.md:166)
- 可观测性与 Q 评分：[`architecture.observability.declaration()`](../architecture.md:318)