# 架构文档分片导航 — 知深学习导师 v2.0

本目录包含从主架构文档（见 ../architecture.md）自动分片的聚焦技术文档，便于研发、测试、运维分别快速查阅。每个分片保留源段落定位与与 PRD 的交叉引用。

来源：[架构主文档](../architecture.md)

分片清单
- 01-引言-驱动因素.md
- 02-高层架构-HLA.md
- 03-技术栈.md
- 04-数据模型-ERD-对象契约.md
- 05-API-设计与错误模型.md
- 06-后端模块设计-服务与适配层.md
- 07-前端应用结构与实现要点.md
- 08-部署与运维-环境与CI-CD.md
- 09-可观测性-日志-指标-追踪.md
- 10-测试策略.md
- 11-安全策略.md
- 12-迁移与演进.md
- 13-项目结构-Monorepo-建议.md
- 14-落地优先事项-与里程碑对齐.md
- 15-附录-提示工程与策略.md
- index-crossref.md（本目录的交叉引用索引）

使用说明
- 面向后端：优先阅读 06、05、04 与 09。
- 面向前端：优先阅读 07、05 与 09。
- 面向DevOps/运维：优先阅读 08、09、11、12、13。
- 若分片与主文档冲突，以 docs/shards 下的分片为准（SSOT），主文档仅作为导航与综述。