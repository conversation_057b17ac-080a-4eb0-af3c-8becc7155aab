# 08 部署与运维（DevOps）— 环境与 CI/CD（源自 架构 §8）

来源：../architecture.md §8（行299–315）

## 8.1 前端部署
- 平台：Vercel/Netlify，主分支自动生产部署；PR 生成 Preview
- 静态资源：CDN 加速、原子化发布、即时回滚

## 8.2 后端部署
- 平台：Render/Fly.io；容器化部署（Dockerfile）
- 运行时：Gunicorn/Uvicorn workers（异步），健康检查、滚动更新

## 8.3 环境与配置（环境矩阵与密钥管理）
- 环境：dev / preview / prod（环境矩阵：域名、DB、存储、队列、采样率、日志等级）
  - dev：采样高、日志详、开特性开关
  - preview：与 prod 等价配置，降规模，保接口口径一致
  - prod：最小权限、只读密钥分离、审计开启
- 机密：.env 注入（由平台密钥管理）；密钥分层：应用密钥/第三方密钥/只读密钥；轮换与撤销流程；本地使用 .env.example 协调
- 数据：独立 Supabase 项目；迁移由 Alembic 管理（即使用 Supabase 仍自管迁移脚本，保障可迁移性）

## 8.4 CI/CD（GitHub Actions）
- 作业：lint + 单测 + 集成测试 → 构建 → 部署
- 失败通知：Slack/邮件
- 版本策略：主分支保护 + 语义化版本（可选）

交叉引用
- 项目结构与 Monorepo：[`architecture.project_structure.declaration()`](../architecture.md:355)
- 可观测性：[`architecture.observability.declaration()`](../architecture.md:318)