# 05 API 设计与错误模型 — 聚焦分片（源自 架构 §5）

来源：../architecture.md §5（行166–215, 217–197），已按 FR10 回退/恢复、trace_id 贯通与 Q 评分采集更新

## 5.1 鉴权与会话
- MVP：允许匿名会话（sessionToken 绑定本地）；后续接入 Supabase Auth（JWT）
- 接入 JWT 后：需要持久化的接口校验 JWT，并实施 RLS（行级安全）
- 追踪要求：所有请求必须携带 trace_id（header: x-trace-id 或 body.trace_id）；网关可在缺失时生成并透传到响应体

## 5.2 REST Endpoints（最小集 + 回退/恢复语义）
1) POST /api/sessions
请求：{ text: string, title?: string, trace_id?: string }
响应：{ sessionId: string, documentId: string, trace_id: string }

2) GET /api/sessions
请求：headers: { x-trace-id }
响应：{ sessions: [{ id, title, updatedAt, progress? }], trace_id: string }

3) GET /api/sessions/{id}
请求：headers: { x-trace-id }
响应：{ session, document, sections, lastSummaryVersion, viewState, trace_id }

4) POST /api/sessions/{id}/messages
请求：{ content: string, trace_id?: string }
响应：{ message, updatedSummaryVersionId?, citations?, trace_id }

5) GET /api/sessions/{id}/messages
请求：headers: { x-trace-id }
响应：{ messages: ConversationMessage[], trace_id }

6) GET /api/sessions/{id}/summary?version={vid}
请求：headers/body 含 trace_id
响应：{ versionId, nodes: SummaryNode[], state: "ok" | "fallback", reason?: "parse_failed"|"schema_mismatch"|"timeout"|"budget", trace_id }

7) POST /api/sessions/{id}/summary/rebuild
请求：{ trace_id?: string }
响应：{ versionId, trace_id }

8) POST /api/sessions/{id}/state
请求：{ viewState, trace_id?: string }
响应：{ ok: true, trace_id }

9) POST /api/sessions/{id}/summary/fallback/recover
说明：回退态下尝试恢复为结构化摘要
请求：{ strategy?: "rebuild" | "incremental", trace_id?: string }
响应：{ versionId, state: "ok" | "fallback", trace_id }

注：state 字段用于标识当前摘要是否处于回退态。fallback 时前端应禁用节点映射交互并显示固定文案（见 PRD/UX）。

## 5.3 统一错误模型与版本策略（扩展）
```json
{
  "error": {
    "code": "UNIQUE_ERROR_CODE",
    "message": "用户可读错误信息",
    "details": { "field": "x", "reason": "y" },
    "trace_id": "uuid",
    "hint": "可选的下一步指引"
  }
}
```
- 常用 code：VALIDATION_ERROR、BUDGET_EXCEEDED、NOT_FOUND、INTERNAL_ERROR、AUTH_REQUIRED、STRUCTURE_PARSE_FAILED、STRUCTURE_SCHEMA_MISMATCH、PROVIDER_TIMEOUT、API_DEPRECATED、API_UNSUPPORTED_VERSION
- 版本策略：
  - 版本字段：响应 header: x-api-version；请求 header: x-api-version（可选）
  - 生命周期：Active → Deprecated（告警+API_DEPRECATED）→ Sunset（API_UNSUPPORTED_VERSION）
  - 弃用流程：发布说明→灰度→告警窗口≥30天→切换开关
- 语义映射：
  - STRUCTURE_PARSE_FAILED / STRUCTURE_SCHEMA_MISMATCH → 触发 FR10 回退（state=fallback，nodes=[]，固定文案）
  - PROVIDER_TIMEOUT → 触发回退或重试；返回 hint 建议重试或缩小范围
  - BUDGET_EXCEEDED → 引导缩小焦点或降低模型路由

## 5.4 速率限制与预算治理（初版）
- 每会话 token 上限（后端内存/Redis 配额可选）
- 达上限：优先摘要压缩；继续超限 → 返回 BUDGET_EXCEEDED 并引导缩小焦点
- 观测要求：响应体包含 trace_id；服务端记录 cost_tokens、retry_count、fallback_state，用于指标聚合与 Q 评分输入

## 5.5 与 PRD/架构 的契合点
- 引用徽章与有效引用率依赖 ConversationMessage.citations 完备性（字段：sectionId/offsetStart/offsetEnd/confidence/valid）
- 摘要版本化需保证 SummaryNode 结构一致性，支持重建与回滚；state 字段向前端暴露回退态
- Q 评分采集口径对齐 §9：结构化通过率、引用完整率、用户标注正向率三项作为输入

交叉引用
- PRD API 契约：[`prd.api.declaration()`](../prd.md)
- 架构 后端模块/服务：[`architecture.backend_modules.declaration()`](../architecture.md:217)
- 架构 可观测性与 Q 评分：[`architecture.observability.declaration()`](../architecture.md:318)
- 架构 API 扩展（trace_id/回退恢复）：[`architecture.api.declaration()`](../architecture.md:166)