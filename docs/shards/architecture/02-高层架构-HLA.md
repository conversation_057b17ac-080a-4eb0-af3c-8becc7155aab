# 02 高层架构（HLA）— 聚焦分片（源自 架构 §2）

来源：../architecture.md §2（行35–90）

## 2.1 技术摘要
- 前端：Vite + React SPA，负责双栏 UI、对话与摘要呈现、锚点跳转、埋点上报
- 后端：FastAPI 单体，充当 API 网关与编排核心，提供上下文装配、模型路由、成本治理、版本化摘要
- 基础设施（MVP）：Supabase 提供 Postgres、Auth、Storage 以加速落地
- 第三方：LLM Provider（OpenAI / Claude / Gemini）通过适配层统一调用

相对 V2 草稿的新增/变更
- 新增：任务编排分层策略（V2 轻任务、V2.1+ 引入 Celery/Redis 承载重任务）
- 变更：前端默认右侧为“动态摘要”主视图（原文为次级 Tab）
- 新增：CN 地域迁移路径与回滚 SOP（避免供应商锁定）
- 补充：SLO 阈值与组件依赖清单

## 2.2 架构风格
- 前端：CSR 单页应用（后续可扩展为 SSR/混合渲染）
- 后端：API Gateway + Monolith（后续提取服务的演进路径清晰）
- 基础设施：BaaS 优先，避免前端直连 DB/Storage

## 2.3 交互拓扑（文字化）
- 浏览器（Vite+React SPA，右栏默认“动态摘要”） → HTTPS/JSON → FastAPI 网关/编排
- 认证流程：SPA ↔ Supabase Auth（SDK）
- FastAPI：
  - 读写 PostgreSQL（连接池/SQL）
  - 访问 Object Storage（SDK/API）
  - 调用 LLM Provider（LLM SDK）
  - 入队 Redis（任务队列）→ Celery（V2.1+）
  - 轻任务使用 Edge Functions/后台任务（V2）

## 2.4 调用约束
- 前端仅与 FastAPI 交互（除认证 SDK 外），严禁直连 DB/Storage
- FastAPI 统一出入口：鉴权、路由、编排、治理、观测

## 2.5 核心架构模式
- API 网关：统一鉴权、限流、追踪（trace_id）与错误模型
- 适配器模式：DB/Storage/Auth/LLM 经接口隔离，便于供应商切换
- 策略模式（模型路由）：按问题难度/预算策略动态选择中/重模型
- 任务编排分层：
  - V2：轻任务（ingest/summarize 小文本）采用 FastAPI 后台任务或 Edge Functions，快速返回，失败回退
  - V2.1+：重任务（大文件/抓取/OCR/TTS）通过 Redis 队列 + Celery Worker，支持幂等键与补偿
- 事件/回调：摘要重建、异步进度查询（后续演进）

交叉引用
- PRD 视图/摘要优先与可追溯：[`prd.FR7.declaration()`](../prd.md)
- PRD 结构化失败回退：[`prd.FR10.declaration()`](../prd.md)
- 架构 任务分层细节：[`architecture.tasks.declaration()`](../architecture.md:274)