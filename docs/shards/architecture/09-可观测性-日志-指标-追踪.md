# 09 可观测性（Observability）— 日志/指标/追踪（源自 架构 §9）

来源：../architecture.md §9（行318–324），已引入 Q 评分、fallback 观测与 trace_id 端到端口径

## 9.1 日志（Logging）
- 结构化日志：JSON 格式，字段建议包含
  - level, ts, trace_id, route, latency_ms, cost_tokens, user/session ids(脱敏/散列), error_kind?, state?
- 前端注入 trace_id，后端全链路透传到每条日志
- 在关键步骤记录分段日志与 span 关联：
  - generate_raw, pydantic_validate, fallback_decision, persist_version, recover_attempt
- 错误日志与业务告警日志分流，便于检索与告警策略配置

## 9.2 指标与SLO（Metrics & SLO）
- 技术指标：请求率/错误率/延迟（P50/P95/P99）；会话 token 消耗；retry_count
- 业务指标：有效引用率、TTR、误链率、单位洞见成本（见 PRD §11）
- 质量评分 Q（记录用，不设阻断门槛）：
  - 定义：Q = 0.5×结构化通过率 + 0.3×引用完整率 + 0.2×用户标注正向率
  - 结构化通过率 = 结构化摘要校验通过次数 / 摘要生成总次数
  - 引用完整率 = 有 citations 且 offsets 完整的回答次数 / 回答总次数
  - 用户标注正向率 = 用户对摘要/回答的正向反馈次数 / 总反馈次数
- 回退态相关指标：
  - fallback_count、fallback_ratio、fallback_reason 分布（parse_failed/schema_mismatch/timeout/budget）
  - recover_attempts、recover_success、recover_success_rate
- 聚合维度：按 route、session、model、reason 分组；保留 trace 采样链接

SLO 建议（按子系统）
- 前端渲染：首屏 TTI P50<2.5s、P95<4s；交互响应 P95<150ms
- API 网关：请求成功率 ≥ 99.5%；延迟 P95<1s（不含LLM）
- 摘要生成：结构化通过率 ≥ 85%；fallback_ratio ≤ 15%；恢复成功率 ≥ 70%
- 任务编排（V2.1+）：入队至开始处理 P95<5s；任务成功率 ≥ 99%
- 观测数据新鲜度：关键指标延迟 < 1m；追踪采样 ≥ 10%（高价值路由 100%）

## 9.3 分布式追踪（Tracing）
- trace_id 从前端产生或注入 header
- 后端在关键步骤打 span：检索/LLM 调用/DB 访问/摘要合并/pydantic_validate/fallback_decision/recover_attempt
- 结合日志字段进行跨域定位（前端→后端→LLM/DB）

## 9.4 告警（Alerting）
- 错误率阈值、延迟阈值、预算异常（突增）触发告警
- 新增：fallback 比例显著上升（环比/同比）告警；Q 评分连续下降告警
- 通道：Slack/邮件或等效渠道
- 关联 runbook：附带常见问题与排障 SOP 链接

## 9.5 数据出入口与落库
- 前端：apiClient 注入 x-trace-id；埋点回传 TTR、反馈正负向、引用点击有效性
- 后端：聚合并写入 METRICS.process/outcome/experience/cost；支持按 trace_id 和 session 维度检索

交叉引用
- PRD 指标与验收：[`prd.milestones_metrics.declaration()`](../prd.md)
- 架构 API 扩展（trace_id/回退/恢复）：[`architecture.api.declaration()`](../architecture.md:166)
- 架构 Q 评分与日志字段：[`architecture.observability.declaration()`](../architecture.md:318)