# 14 落地优先事项（与里程碑对齐）— 聚焦分片（源自 架构 §14）

来源：../architecture.md §14（行379–395）

## 14.1 后端
- API 基座（鉴权/错误模型/trace 中间件）
- ContextService + SummaryService（滚动增量 + 版本）
- LLM Adapter（中/重模型路由 + 超时/重试）
- 成本治理（预算器 + 压缩/熔断）

## 14.2 前端
- 双栏布局 + Tabs + Anchor 定位与高亮
- 消息流/骨架屏/错误重试
- 顶部预算/路由状态条
- 埋点与指标上报（TTR/引用率/误链率）

## 14.3 DevOps
- GitHub Actions（lint/测/构/发）
- 环境变量与密钥管理
- 监控与日志聚合

与 PRD 里程碑对齐（执行清单口径）
- M1 可控上下文 + 滚动摘要
  - 后端：ContextService/裁剪策略、SummaryService/增量合并、错误模型基座
  - 前端：双栏+锚点、消息流、trace_id 注入
  - 验收：样例文档完整走通；TTR 指标采集上线
- M2 结构化摘要 + 回看效率
  - 后端：PydanticAI 校验引入到摘要与回答路径；fallback/recover API 完整
  - 前端：回退态 UI、恢复入口、引用徽章与“请求证据”
  - 验收：结构化通过率≥85%；fallback_ratio≤15%
- M3 成本与稳健性
  - 后端：预算器/限流/降级策略、错误码口径统一
  - 前端：预算提示/降级提示
  - 验收：BUDGET_EXCEEDED 正确触发并引导；关键接口 P95<1s（不含 LLM）
- M4 学习成效与报告 → 指标上报与观测面板对接

交叉引用
- PRD 里程碑与验收：[`prd.milestones.declaration()`](../prd.md)
- 可观测性：[`architecture.observability.declaration()`](../architecture.md:318)