# 15 附录：提示工程与策略（源自 架构 §15）

来源：../architecture.md §15（行398–403），已补充结构化失败识别信号与降级提示策略（FR10）

## 15.1 回答与引用策略
- 引用优先模板
  - 回答必须尽量引用 {sectionId, offsetStart, offsetEnd}，并包含 confidence
  - 缺失可引用片段时，明确标注“推断”，并提供“请求证据”快捷入口（前端联动）

## 15.2 摘要增量策略
- 仅合并新轮对话 delta，避免整体重排，维持节点稳定性（便于回看与对齐 TTR）
- 需要时触发“重建摘要”，对版本进行递增记录与可回滚

## 15.3 模型路由策略
- 默认中模型
- 遇复杂指令或长论证链时临时升级重模型
- 会话预算逼近时降级与压缩（结合 CostGuard 预算治理）

## 15.4 失败识别与降级提示（与 FR10 对齐）
- 失败识别信号：来自服务层的 ErrorKind（parse_failed | schema_mismatch | timeout | budget_exceeded | citations_invalid）
- 降级策略：
  - 摘要路径：当 ErrorKind∈{parse_failed,schema_mismatch,timeout} 时，降级为“纯文本摘要 + nodes=[]”，并返回 state=fallback 与固定 UX 文案 key: summary.fallback.generic
  - 回答路径：当 citations 校验不通过时，去除 citations 字段并追加“推断”标签与请求证据入口
- 用户提示（固定文案要点）：
  - “当前为简化摘要，部分交互已暂时禁用”
  - 提供“重试/恢复结构化”入口（POST /summary/fallback/recover）
  - 提供可操作建议（hint）：缩小范围、降低复杂度或稍后重试
- 观测联动：
  - 记录 fallback_count、fallback_reason、recover_attempts、recover_success；所有提示带 trace_id 便于端到端排障
  - 与 Q 评分输入对齐：失败导致“结构化通过率”分母+1、分子不变

一致性要点
- 引用/摘要的 offsets 口径一致，支撑“摘要节点→原文定位”和“引用徽章”
- 重建/回滚通过 version 进行版本化管理，与前端 UI 控件协同
- 前端根据 state=fallback 禁用节点映射交互，恢复成功后自动恢复

交叉引用
- PRD FR5 引用徽章/推断标签：[`prd.FR5.declaration()`](../prd.md)
- PRD FR6/FR7/FR10 摘要增量、可追溯与回退：[`prd.FR6_7_10.declaration()`](../prd.md)
- 架构 可观测性与 Q 评分：[`architecture.observability.declaration()`](../architecture.md:318)
- 架构 API 扩展（trace_id/回退恢复）：[`architecture.api.declaration()`](../architecture.md:166)