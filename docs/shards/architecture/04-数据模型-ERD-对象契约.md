# 04 数据模型（ERD 与对象契约）— 聚焦分片（源自 架构 §4）

来源：../architecture.md §4（行100–164）

## 4.1 ERD（MVP）
关系概览（以 Mermaid 在主文档呈现）
- USERS ||--o{ SESSIONS : has
- SESSIONS ||--|{ MESSAGES : contains
- SESSIONS ||--|{ SUMMARIES : has
- SESSIONS ||--|{ SECTIONS : divides
- SESSIONS ||--o{ METRICS : aggregates

关键表字段
USERS
- id(UUID), email, created_at

SESSIONS
- id(UUID), user_id(UUID), title, document_id(UUID)
- focus_section_index(int), view_state(json)
- created_at, updated_at

SECTIONS
- id(UUID), document_id(UUID), order(int)
- offset_start(int), offset_end(int)
- text(text)

MESSAGES
- id(UUID), session_id(UUID), role(string: user|assistant)
- content(text), citations(json: [{sectionId, offsetStart, offsetEnd, confidence, valid}])
- costs(json: {promptTokens, completionTokens, model}), created_at

SUMMARIES
- id(UUID), session_id(UUID), version(int)
- nodes(json: [{id,type,text,refs:[{sectionId,offsetStart,offsetEnd,confidence}]}])
- created_at

METRICS
- id(UUID), session_id(UUID)
- process(json), outcome(json), experience(json), cost(json)
- created_at

## 4.2 关键对象契约（与 PRD 对齐）

变更管控
- Schema 变更等级：Patch（兼容新增字段）/ Minor（新增必填或约束放宽，有迁移脚本）/ Major（破坏性，需双写/灰度）
- 流程：提案（变更说明+影响面）→ 评审 → 迁移脚本/Alembic → 双写/读新写旧（必要时）→ 验证 → 切换 → 清理
- 回滚：保留 N 版本迁移脚本；失败即刻回退；保留数据备份与事件重放能力

- SummaryNode：type ∈ {concept, evidence, counter, reference}；refs 关联 SECTIONS + offsets；version 控制回滚/重建
- ConversationMessage.citations：服务“引用徽章”与有效引用率计算
- Session.view_state：右栏 tab（original|summary）、滚动位置、摘要版本

一致性要点
- 引用与摘要均以 offsets 为锚，支撑“摘要节点→原文定位”与 TTR 指标
- 版本化的 SummaryNode 保持结构稳定，利于回滚与重建
- citations 字段完备性直接影响“有效引用率”与“误链率”统计

交叉引用
- PRD 数据模型简介：[`prd.data_models.declaration()`](../prd.md)
- 架构 API 设计：[`architecture.api.declaration()`](../architecture.md:166)
- 观测指标与口径：[`architecture.observability.declaration()`](../architecture.md:318)