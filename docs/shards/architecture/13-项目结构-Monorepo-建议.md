# 13 项目结构（Monorepo 建议）— 聚焦分片（源自 架构 §13）

来源：../architecture.md §13（行355–375）

## 13.1 目录提案
```
/phoenix-learning-app
├── apps/
│   ├── backend/              # FastAPI
│   │   ├── app/{api,core,services,adapters,crud,models,main.py}
│   │   ├── tests/
│   │   └── requirements.txt
│   └── frontend/             # Vite + React
│       ├── src/{api,components,hooks,pages,store,styles,App.tsx}
│       ├── package.json
│       └── vite.config.ts
├── packages/
│   └── shared-types/
│       └── src/index.ts
├── docs/
├── .github/workflows/
├── .env.example
└── package.json
```

## 13.2 管理策略
- 版本：语义化版本与变更日志（可选 Changesets）
- 依赖：根 package.json 管理 tooling（lint/format/test），apps 独立运行
- CI：按路径触发（仅变更子项目触发对应流水线）
- 代码规范：统一 ESLint/Prettier 与 Python lint（ruff/flake8）

交叉引用
- DevOps/CI-CD：[`architecture.devops.declaration()`](../architecture.md:299)
- 技术栈与目录：[`architecture.tech_stack.declaration()`](../architecture.md:90)