# Architecture Shards Cross-Reference Index

目的：建立分片间与源文档/PRD 的交叉引用，支持角色定向查阅与一致性校验（章节→源行号→相关分片）。

来源主文档：../architecture.md  
关联 PRD：../prd.md

## 角色导览
- 后端/平台：02-HLA，04-数据模型，05-API，06-后端模块，09-可观测性，11-安全，08-DevOps
- 前端：02-HLA，07-前端结构，05-API（客户端契约），09-可观测性
- QA：05-API，09-可观测性，10-测试策略，PRD 里程碑与验收
- PO/PM：02-HLA，14-落地优先事项，12-迁移与演进，03-技术栈（概览）

## 交叉引用矩阵
- 01-引言-驱动因素.md
  - 源：architecture.md:10-31
  - 关联：PRD 总述 [`prd.declaration()`](../prd.md:1)
- 02-高层架构-HLA.md
  - 源：architecture.md:35-90
  - 关联：PRD 视图/回退 FR7/FR10 [`prd.declaration()`](../prd.md:63), [`prd.declaration()`](../prd.md:103)
- 03-技术栈.md
  - 源：architecture.md:90-98
  - 关联：项目结构 [`architecture.project_structure.declaration()`](../architecture.md:355)
- 04-数据模型-ERD-对象契约.md
  - 源：architecture.md:100-164
  - 关联：PRD 数据模型 [`prd.declaration()`](../prd.md:154)
- 05-API-设计与错误模型.md
  - 源：architecture.md:166-215, 217-197
  - 关联：PRD §7 API 契约 [`prd.api.declaration()`](../prd.md:180) ｜ 分片 [`prd.shard_api.declaration()`](../prd/07-API-契约.md:1)
  - 新增对齐：trace_id 贯通、回退/恢复接口、错误码扩展（STRUCTURE_PARSE_FAILED/SCHEMA_MISMATCH/PROVIDER_TIMEOUT）
- 06-后端模块设计-服务与适配层.md
  - 源：architecture.md:217-277
  - 关联：PydanticAI 回退/一致性 [`prd.FR10.declaration()`](../prd.md:103)
  - 新增对齐：state=fallback 对前端禁用交互；recover 机制；ErrorKind 映射
- 07-前端应用结构与实现要点.md
  - 源：architecture.md:278-296
  - 关联：PRD IA/UX [`prd.ia_ux.declaration()`](../prd.md:132)
  - 衔接说明：右栏默认“动态摘要”（PRD §3/§7 已更新为摘要优先）
- 08-部署与运维-环境与CI-CD.md
  - 源：architecture.md:299-315
  - 关联：Monorepo/流水线 [`architecture.project_structure.declaration()`](../architecture.md:355)
- 09-可观测性-日志-指标-追踪.md
  - 源：architecture.md:318-324
  - 关联：PRD §11 指标与里程碑 [`prd.metrics.declaration()`](../prd.md:276) ｜ 分片测试清单 [`prd.shard_testing.declaration()`](../prd/10-测试与验收清单.md:1)
  - 新增对齐：Q 评分、fallback 指标（fallback_ratio/recover_success_rate）、trace_id 采集位置
- 10-测试策略.md
  - 源：architecture.md:327-333
  - 关联：PRD §10 测试与验收清单 [`prd.testing.declaration()`](../prd.md:255) ｜ 分片 [`prd.shard_testing.declaration()`](../prd/10-测试与验收清单.md:1)
  - 新增对齐：回退路径测试（固定文案/禁用/恢复/trace 回传）
- 11-安全策略.md
  - 源：architecture.md:336-343
  - 关联：API 鉴权与错误模型 [`architecture.api.declaration()`](../architecture.md:166)
- 12-迁移与演进.md
  - 源：architecture.md:346-352
  - 关联：任务分层与网关 [`architecture.tasks.declaration()`](../architecture.md:274)
- 13-项目结构-Monorepo-建议.md
  - 源：architecture.md:355-375
  - 关联：技术栈/CI-CD [`architecture.tech_stack.declaration()`](../architecture.md:90)
- 14-落地优先事项-与里程碑对齐.md
  - 源：architecture.md:379-395
  - 关联：PRD §9 里程碑与验收 [`prd.milestones.declaration()`](../prd.md:237)
- 15-附录-提示工程与策略.md
  - 源：architecture.md:398-403
  - 关联：PRD FR5/FR6/FR7/FR10 [`prd.fr_5_6_7_10.declaration()`](../prd.md:62)
  - 新增对齐：失败识别与降级提示（state=fallback 提示语；恢复入口；hint 建议）

## 一致性检查要点
- 引用与摘要 offsets 口径一致：前端渲染/后端校验/指标统计一致
- 回退策略（结构化失败）对 UI 与 API 的约束同步更新；state=fallback 必须透传到响应并触发禁用交互
- 预算治理阈值与错误码在 PRD/NFR 与架构/API 同步（含 STRUCTURE_* 与 PROVIDER_TIMEOUT）
- 指标口径：TTR/有效引用率/误链率/单位洞见成本/Q 评分 在 PRD/MVP 与可观测性一致
- 追踪：trace_id 前端注入、后端透传；重要接口请求与响应体需可检索 trace_id
