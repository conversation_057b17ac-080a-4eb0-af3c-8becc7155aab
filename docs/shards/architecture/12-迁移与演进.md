# 12 迁移与演进（Roadmap-Oriented Architecture）— 聚焦分片（源自 架构 §12）

来源：../architecture.md §12（行346–352）

## 12.1 供应商迁移
- 通过 adapter 隔离 DB/Storage/Auth/LLM，配置驱动切换，避免供应商锁定
- 约束：适配层需保持契约稳定，对上屏蔽差异、对下实现最小依赖

## 12.2 服务拆分路径
- 当会话/摘要达到规模后，从 Monolith 抽出 Summary/Message 服务为独立微服务
- 网关（FastAPI）保留统一出入口与跨服务编排

## 12.3 能力增强（第二阶段）
- 检索增强：RAG/向量库（pgvector/Weaviate/ES）
- 多模态：语音模式与多格式导入，以独立边车服务与队列接入，保持网关不变

交叉引用
- 任务编排分层与异步：[`architecture.tasks.declaration()`](../architecture.md:274)
- API 网关与适配层：[`architecture.backend_modules.declaration()`](../architecture.md:217)