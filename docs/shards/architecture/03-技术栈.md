# 03 技术栈（源自 架构 §3）

来源：../architecture.md §3（行90–98）

## 3.1 前端
- React 18.x、Vite 5.x、TypeScript
- 状态管理：Zustand/Redux（二选一）
- 路由：React Router
- 样式：CSS Modules/Tailwind（二选一）

## 3.2 后端
- Python 3.11+、FastAPI 0.110+、Pydantic v2
- SQLAlchemy + asyncpg、httpx、tenacity（重试）

## 3.3 基础设施与部署
- Supabase（Postgres 15、Auth、Storage）
- 前端 Vercel/Netlify；后端 Render/Fly.io（容器化）
- 观测：结构化日志（JSON）、平台内置监控、可选 Sentry

交叉引用
- 高层架构摘要：[`architecture.hla.declaration()`](../architecture.md:35)
- 项目结构与Monorepo：[`architecture.project_structure.declaration()`](../architecture.md:355)