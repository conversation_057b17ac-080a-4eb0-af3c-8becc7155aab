# 01 引言与架构驱动因素（源自 架构 §1）

来源：../architecture.md §1（行10–31）

## 1.1 文档目的
提供端到端的架构蓝图与实施约束，指导研发以最小风险交付“动态上下文 + 动态摘要”的核心学习体验，并确保后续演进具备良好的可迁移性与可维护性。

## 1.2 架构驱动因素
- 核心体验：支持长程对话、动态摘要（滚动增量）、可追溯（摘要节点→原文）
- UI/UX：实现“双栏心智模型（左对话｜右内容）”，最小干扰的沉浸式学习
- 技术选型：FastAPI 后端、Vite+React 前端、Supabase（Postgres/Auth/Storage）作为初期 BaaS
- 演进空间：通过适配层与网关策略，支持未来迁移至自建或国内云厂商

## 1.3 启动模板与现状
- 绿地项目（Greenfield），按本文档新建仓库与骨架

## 1.4 变更日志（摘要）
- 2025-08-05 v2.0：定稿；补充测试、安全、可观测性策略
- 2025-08-05 v1.5：新增部署与运维策略
- 2025-08-05 v1.4：新增项目结构定义
- 2025-08-05 v1.3：新增 API 与错误模型
- 2025-08-05 v1.2：新增数据模型
- 2025-08-05 v1.1：新增高层架构与技术栈
- 2025-08-05 v1.0：引言与驱动因素

交叉引用
- PRD 链接：[`prd.declaration()`](../prd.md)
- UX 规格：[`ux-spec.declaration()`](../ux-spec.md)
- MVP 验收：[`mvp-criteria.declaration()`](../mvp-criteria.md)