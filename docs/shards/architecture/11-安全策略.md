# 11 安全策略 — 认证/授权/传输/存储/依赖（源自 架构 §11）

来源：../architecture.md §11（行336–343）

## 11.1 认证（Authentication）
- MVP：可匿名访问；使用 sessionToken 绑定本地
- 未来：接入 Supabase Auth（JWT），后端校验 JWT，有效期与刷新策略遵循平台默认或自定义策略

## 11.2 授权（Authorization）
- 行级安全（RLS）策略启用后，基于用户上下文限制数据访问
- 服务端接口按资源进行授权校验，禁止越权访问

## 11.3 传输安全
- 全链路 HTTPS/TLS
- 前端与后端的敏感头与 token 通过安全存储与最小暴露传递

## 11.4 存储与数据最小化
- 仅存储最小必要数据，敏感信息尽量脱敏
- 对象存储采用签名 URL 或策略控制访问时效与权限

## 11.5 依赖安全
- 后端：pip-audit；前端：npm audit
- Dependabot/等效工具开启并设定升级策略与合规门槛

交叉引用
- API 与错误模型：[`architecture.api.declaration()`](../architecture.md:166)
- 部署与机密管理：[`architecture.devops.declaration()`](../architecture.md:299)