# 10 测试策略 — 单元/集成/可用性/负载（源自 架构 §10）

来源：../architecture.md §10（行327–333）

## 10.1 单元测试
- 后端：Pytest，覆盖核心服务（ContextService、SummaryService、CostGuard、适配器）
- 前端：Vitest + React Testing Library，覆盖组件/页面/状态管理
- 覆盖率目标：≥ 80%

## 10.2 集成测试
- API 端到端：使用 mock/record 的 LLM 适配器
- 场景：创建会话 → 发送消息 → 生成/更新摘要 → 回滚/重建摘要 → 保存/恢复视图状态

## 10.3 可用性测试
- 依据 UX 验收清单（TTR、丢失感、自评问卷）
- 三典型场景：论文 / 法律 / RFC

## 10.4 负载与稳健
- 接口 P50 < 3s（含 LLM），退避/熔断路径可测
- 误链率、有效引用率、异常中断率指标校验

交叉引用
- PRD 测试与验收清单：[`prd.testing_acceptance.declaration()`](../prd.md)
- 观测指标：[`architecture.observability.declaration()`](../architecture.md:318)