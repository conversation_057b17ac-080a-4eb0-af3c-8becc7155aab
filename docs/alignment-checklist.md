# 文档一致性审阅清单 — 知深学习导师

本清单对当前关键文档进行一致性核对，并给出修订建议与定位。范围包含：
- 标准版项目简报 [`project_brief.declaration()`](docs/project-brief.md:1)
- 产品需求文档（PRD） [`prd.declaration()`](docs/prd.md:1)
- UI/UX 规格说明书 [`ux-spec.declaration()`](docs/ux-spec.md:1)
- 全栈架构文档 [`architecture.declaration()`](docs/architecture.md:1)
- MVP 验收标准与指标口径 [`mvp-criteria.declaration()`](docs/mvp-criteria.md:1)

状态标记：✅ 一致 ｜ ⚠️ 需修订 ｜ ❗需决策

---

## 1. 核心用户体验与默认视图

检查点
- 右侧默认视图是否为“动态摘要”，原文为次级 Tab
- 双栏心智模型是否在各文档中口径一致

结论
- PRD：已将 FR3/FR7 统一为“右侧默认动态摘要（摘要优先）” ✅
- UX：待将默认视图改为“摘要优先”，与架构对齐 ⚠️
- 架构：已注明“右侧默认动态摘要” ✅
- 简报：未强制默认视图口径，可跟随统一
- 验收标准：未涉及默认视图，不影响

修订建议
- UX 同步将默认视图改为“摘要优先”
  - UX 修改位置：学习会话页布局与流程描述处 [`ux-spec.declaration()`](docs/ux-spec.md:1)

---

## 2. 动态摘要结构与可追溯

检查点
- 摘要节点四分类（concept/evidence/counter/reference）是否全链条一致
- 摘要→原文单向跳转在各文档是否一致
- 摘要版本/回滚是否在 UX/PRD/架构中一致

结论
- PRD：FR6 已增补“摘要历史版本与回滚”强口径；点击后原文高亮2–3秒已标注 ✅
- UX：节点四类、点击跳转原文、版本与回滚已覆盖 ✅
- 架构：SummaryNode 契约、版本机制明确 ✅
- 验收标准：TTR 指标、误链率、覆盖率定义明确 ✅

修订建议
- 无（保持抽检）
  - 参考锚点：[`PRD FR6.declaration()`](docs/prd.md:80)

---

## 3. 任务编排分层与性能目标

检查点
- V2 轻任务（后台/Edge）与 V2.1+ 重任务（Redis+Celery）一致性
- 性能与SLO（P50/P95、摘要时延）是否贯穿

结论
- 架构：已加入分层与SOP ✅
- PRD：未显式说明任务分层与异步策略 ⚠️（可保持PRD简洁，但需说明“异步与回退”的体验）
- UX：未体现异步任务与进度反馈（除加载骨架），可补“长任务状态与重试入口” ⚠️
- 验收标准：有响应时间与会话成本指标，但未具体到 ingest/summarize 分项 ⚠️

修订建议
- PRD 增补“长任务时的用户反馈与回退行为”段落（非功能/体验）
  - 修改位置：[`NFR2/NFR3.declaration()`](docs/prd.md:1)
- UX 增补“长任务状态条/通知与重试入口”
  - 修改位置：[`微交互—发送与生成.declaration()`](docs/ux-spec.md:1)
- 验收标准 增补“ingest/summarize”独立时延SLO（如 summarize 平均<1.5s）
  - 修改位置：[`里程碑3/4指标段.declaration()`](docs/mvp-criteria.md:1)

---

## 4. 结构化输出与 PydanticAI 校验

检查点
- PydanticAI 作为结构化摘要与引用校验是否在文档中对齐
- 失败回退策略是否在产品与体验中体现

结论
- 架构：6.1/6.2/§5 已明确 PydanticAI 校验、回退/恢复接口与错误码扩展 ✅
- PRD：FR10“结构化失败回退”已新增并与“推断标签”解耦 ✅
- UX：待补“摘要回退提示文案与禁用映射交互”的UI规范 ⚠️

修订建议
- UX 增补“摘要回退提示文案与禁用映射交互”的UI规范
  - 修改位置：[`摘要与原文跳转.declaration()`](docs/ux-spec.md:1)

---

## 5. 错误模型、trace_id 与可观测性

检查点
- 统一错误结构与 trace_id 是否在各文档一致
- UX 是否定义用户可见的错误文案与 trace 显示位

结论
- 架构：统一错误模型并扩展 STRUCTURE_* 与 PROVIDER_TIMEOUT；trace 中间件与 x-trace-id 贯通 ✅
- PRD：API 契约含 trace_id 贯通与错误模型；测试清单含 trace 校验 ✅
- UX：有“错误重试与traceId”文案位置 ✅
- 验收标准：无直接矛盾；埋点段落强调 trace 贯穿 ✅

修订建议
- 可选：在 PRD/UX 加入“错误码到用户文案映射表”的附录（后续）
  - 位置：[`PRD附录.declaration()`](docs/prd.md:1) / [`UX文案规范.declaration()`](docs/ux-spec.md:1)

---

## 6. 成本治理与指标

检查点
- Token 预算器、模型路由、单位洞见成本是否一致
- 指标口径：有效引用率、TTR、丢失感、重述准确率一致

结论
- 架构/PRD/验收标准：一致；新增“单位洞见成本”采集与看板检索 ✅
- UX：顶部预算/路由状态条已定义 ✅
- 简报：口径不冲突 ✅

修订建议
- 无强制。可在 UX 增补“预算接近与熔断”更明确的色彩与文案规范（已基本覆盖）
  - 位置：[`预算与模型路由提示.declaration()`](docs/ux-spec.md:1)

---

## 7. 默认输入与MVP范围

检查点
- 仅支持纯文本粘贴、最大长度与分段策略一致

结论
- PRD/UX/架构/验收标准：一致 ✅
- 建议在 PRD 明确输入最大长度与分段策略（用于前端错误提示口径一致） ⚠️

修订建议
- PRD 增补输入上限（字符/Token估算）与分段失败提示
  - 位置：[`FR1/FR2 输入约束.declaration()`](docs/prd.md:1)
- UX 增补“超长异常提示文案”
  - 位置：[`起始页—错误态.declaration()`](docs/ux-spec.md:1)

---

## 8. 链接与锚点一致性

检查点
- 文档间交叉引用是否均为可用标题锚点
- 仍存在 path:line 形式是否保留为旁注

结论
- 已按方案A修复主要链接 ✅
- 仍需抽样检查新生成的 architecture.md 与 ux-spec.md/PRD 中的交叉链接是否全部有效 ⚠️（小概率）

修订建议
- 若发现断链，按已采用的锚点规范统一修正
  - 位置：[`链接巡检.declaration()`](docs/architecture.md:1) / [`ux-spec.declaration()`](docs/ux-spec.md:1) / [`prd.declaration()`](docs/prd.md:1)

---

## 9. 汇总与执行建议

必须修订（建议立即处理）
1) PRD：默认视图改为“摘要优先”（FR3/FR7）；增补摘要版本/回滚强口径；补充结构化失败回退说明；补充输入上限与分段异常提示。  
2) UX：默认视图改为“摘要优先”；补充长任务状态与重试；补充结构化失败回退与禁用映射交互的规范；补超长提示文案。  
3) 验收标准：补充 summarize/ingest 独立SLO（如 summarize 平均<1.5s）。

可选优化
- PRD/UX 附录：错误码→用户文案映射表
- 链接巡检与补齐

---

## 10. 下一步（执行清单）

- [x] 修订 PRD：FR3/FR7 默认视图为“摘要”；新增“摘要版本与回滚”条款；补“结构化失败回退”；补“输入上限/分段异常提示”
- [ ] 修订 UX：Session 页默认摘要；长任务状态条/重试；摘要回退UI；起始页超长错误文案
- [ ] 修订 验收标准：加入 summarize/ingest 的SLO阈值；纳入 Q 评分记录项（不设阻断门槛）
- [ ] 链接巡检：抽样 architecture.md / ux-spec.md / prd.md 的锚点有效性
- [ ] 统一术语：在各文档引言处统一“动态摘要/滚动增量/节点四类”的措辞

（完）